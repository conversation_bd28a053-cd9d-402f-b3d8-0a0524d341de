/*! Tablesaw - v1.0.3 - 2015-01-27
* https://github.com/filamentgroup/tablesaw
* Copyright (c) 2015 Filament Group; Licensed MIT */

table.tablesaw {
  empty-cells: show;
  max-width: 100%;
  width: 100%;
}

.tablesaw {
  border-collapse: collapse;
  width: 100%;
}

/* Structure */

.tablesaw {
  border: 0;
  padding: 0;
}

.tablesaw th,
.tablesaw td {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: .5em .7em;
}

.tablesaw thead tr:first-child th {
  padding-top: .9em;
  padding-bottom: .7em;
}

/* Table rows have a gray bottom stroke by default */

.tablesaw-stack tbody tr {
  border-bottom: 1px solid #dfdfdf;
}

.tablesaw-stack td .tablesaw-cell-label,
.tablesaw-stack th .tablesaw-cell-label {
  display: none;
}

/* Mobile first styles: Begin with the stacked presentation at narrow widths */

@media only all {
  /* Show the table cells as a block level element */

  .tablesaw-stack td,
  .tablesaw-stack th {
    text-align: left;
    display: block;
  }

  .tablesaw-stack tr {
    clear: both;
    display: table-row;
  }

  /* Make the label elements a percentage width */

  .tablesaw-stack td .tablesaw-cell-label,
  .tablesaw-stack th .tablesaw-cell-label {
    display: block;
    padding: 0 .6em 0 0;
    width: 30%;
    display: inline-block;
  }

  /* For grouped headers, have a different style to visually separate the levels by classing the first label in each col group */

  .tablesaw-stack th .tablesaw-cell-label-top,
  .tablesaw-stack td .tablesaw-cell-label-top {
    display: block;
    padding: .4em 0;
    margin: .4em 0;
  }

  .tablesaw-cell-label {
    display: block;
  }

  /* Avoid double strokes when stacked */

  .tablesaw-stack tbody th.group {
    margin-top: -1px;
  }

  /* Avoid double strokes when stacked */

  .tablesaw-stack th.group b.tablesaw-cell-label {
    display: none !important;
  }
}

@media (max-width: 39.9375em) {
  .tablesaw-stack thead td,
  .tablesaw-stack thead th {
    display: none;
  }

  .tablesaw-stack tbody td,
  .tablesaw-stack tbody th {
    clear: left;
    float: left;
    width: 100%;
  }

  .tablesaw-cell-label {
    vertical-align: top;
  }

  .tablesaw-cell-content {
    max-width: 67%;
    display: inline-block;
  }

  .tablesaw-stack td:empty,
  .tablesaw-stack th:empty {
    display: none;
  }
}

/* Media query to show as a standard table at 560px (35em x 16px) or wider */

@media (min-width: 40em) {
  .tablesaw-stack tr {
    display: table-row;
  }

  /* Show the table header rows */

  .tablesaw-stack td,
  .tablesaw-stack th,
  .tablesaw-stack thead td,
  .tablesaw-stack thead th {
    display: table-cell;
    margin: 0;
  }

  /* Hide the labels in each cell */

  .tablesaw-stack td .tablesaw-cell-label,
  .tablesaw-stack th .tablesaw-cell-label {
    display: none !important;
  }
}