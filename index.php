<?php
/*
 The Template for displaying all posts
 */
?>

<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
	<title><?php echo wp_title( '', true, 'left' ); ?></title>


	<?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-news">

	<?php get_template_part('blocks/b_header'); ?>

	<div class="container">
        <div class="content-divider">
    		<div class="row">
    			<div class="col-sm-4 col-md-3 hidden-xs">
    				<?php get_template_part('blocks/b_sidebar_primary'); ?>
    			</div>
    			<div class="col-sm-8 col-md-9 print-col-12 padding-left">

    				<?php //get_template_part('blocks/b_breadcrumbs'); ?>

    				<h1 class="primary-heading"><?php pll_e('News'); ?></h1>
    				<div class="news">
                        <?php
                        $args=array(
                          'post_type' => 'post',
                          'post_status' => 'publish',
                          'posts_per_page' => -1,
                        );

                        $news = new WP_Query($args);
                        if( $news->have_posts() ) { ?>
                            <ul class="news-list">
                                <?php while ($news->have_posts()) : $news->the_post(); ?>
                                    <li class="news-item">
                                        <a href="<?php echo get_the_permalink(); ?>" class="news-link">
                                            <div class="message">
                                                <div class="message-date">
                                                    <?php echo get_the_date('d.m.Y'); ?>
                                                </div>
                                                <h2 class="message-heading">
                                                    <?php echo get_the_title(); ?>
                                                </h2>
                                                <div class="message-intro">
                                                    <?php echo get_the_excerpt(); ?>
                                                </div>
                                            </div>
                                        </a>
                                    </li>

                                <?php endwhile; ?>
                            </ul>
                        <?php } else{ ?>
                            <div>
                                <p>
                                    <?php pll_e('No News'); ?>
                                </p>
                            </div>
                        <?php } ?>
                    </div>
    			</div>
    		</div>
        </div>
	</div>

	<?php get_template_part('blocks/b_mob_social_icons'); ?>

	<?php get_template_part('blocks/b_footer'); ?>

	<?php get_template_part('blocks/b_javascripts'); ?>

</body>
</html>
