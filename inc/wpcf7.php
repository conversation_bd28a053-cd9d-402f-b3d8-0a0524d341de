<?php

/**
 * WPCF7 form answer saving to tsv file for a compaign form
 */
class WS_WPCF7
{
	function __construct()
	{
		add_filter('wpcf7_skip_mail', [$this, 'save_form'], 20, 2);
		add_action('admin_menu', [$this, 'download_admin_menu'], 20, 0);
		add_action('wp_ajax_get_form_result', [$this, 'get_form_result']);
	}

	public function get_form_result()
	{
		if ( ! isset($_GET['id']))
			return;

		$form = WPCF7_ContactForm::get_instance($_GET['id']);
		if ( ! $form)
			return;

		$upload_dir = wp_upload_dir();
		if ($upload_dir['error'])
			return;

		$file_name = $form->pref('ws_save_custom_file') ?: $form->name();
		$file = "{$upload_dir['basedir']}/ws-wpcf7-files/{$file_name}-results.tsv";
		if (file_exists($file)) {
			header($_SERVER["SERVER_PROTOCOL"] . " 200 OK");
			header("Content-Type: text/html; charset=utf-8");
			header("Content-Length: ".filesize($file));
			header("Content-Disposition: attachment; filename={$form->name()}-results.tsv");
			readfile($file);
			exit();
		}
	}

	public function download_admin_menu()
	{
		$integration = add_submenu_page( 'wpcf7',
			'Contact form files',
			'Contact form files',
			'wpcf7_manage_integration',
			'ws-wpcf7-files',
			[$this, 'file_download_template']
		);
		// add_action( 'load-' . $integration, [$this, 'file_download_template'], 10, 0 );
	}

	public function file_download_template()
	{
		$contact_forms = get_posts(['post_type' => 'wpcf7_contact_form', 'numberposts' => -1]);

		$upload_dir = wp_upload_dir();
		if ($upload_dir['error'])
			return;

		$folder = $upload_dir['basedir'] . '/ws-wpcf7-files/';
		$admin_url = admin_url('admin-ajax.php');
		$html = '<div class="wrap">';

		$results = [];
		foreach ($contact_forms as $form_post) {
			$form = WPCF7_ContactForm::get_instance($form_post->ID);
			$file_name = $form->pref('ws_save_custom_file') ?: $form->name();
			if (file_exists("{$folder}{$file_name}-results.tsv")) {
				if (isset($results[$file_name])) {
					$results[$file_name]['posts'][] = $form_post->post_title;
				} else {
					$results[$file_name] = [
						'ID' => $form_post->ID,
						'posts' => [$form_post->post_title],
					];
				}
			}
		}

		foreach ($results as $data) {
			$names = implode(', ', $data['posts']);
			$html .= "<p><a href='{$admin_url}?action=get_form_result&id={$data['ID']}'>Download {$names} results</a></p>";
		}

		$html .= '</div>';
		echo $html;
	}

	public function save_form($skip_mail, $contact_form)
	{
		$file_location = $this->get_file_location($contact_form);
		if ( ! $file_location)
			return $skip_mail;

		$form_type = $contact_form->pref('ws_save_to_tsv');
		if ($form_type == 'tasuta-muugihinnang') {
			// Get fields and remove TAB characters from form results
			$eesmark = str_replace("\t", '', $_POST['muugihinnangu_eesmark'] ?? '');
			$name    = str_replace("\t", '', $_POST['your-name'] ?? '');
			$tel     = str_replace("\t", '', $_POST['your-tel'] ?? '');
			$email   = str_replace("\t", '', $_POST['your-email'] ?? '');
			$address = str_replace("\t", '', $_POST['your-address'] ?? '');
			$date    = date_format(date_create('now', new DateTimeZone('Europe/Tallinn')), 'H:i d.m.Y');

			if ( ! file_exists($file_location)) {
				file_put_contents($file_location, "Postituse aeg\tMüügihinnangu eesmark\tNimi\tTelefon\tEmail\tKinnisvara aadress\n", FILE_APPEND);
			}
			file_put_contents($file_location, "$date\t$eesmark\t$name\t$tel\t$email\t$address\n", FILE_APPEND);

		} else if ($form_type == 'ostusoov') {
			$date    = date_format(date_create('now', new DateTimeZone('Europe/Tallinn')), 'H:i d.m.Y');
			$request = str_replace("\t", '', $_POST['your-ostusoov'] ?? '');
			$name    = str_replace("\t", '', $_POST['your-name'] ?? '');
			$tel     = str_replace("\t", '', $_POST['your-tel'] ?? '');
			$email   = str_replace("\t", '', $_POST['your-email'] ?? '');

			if ( ! file_exists($file_location)) {
				file_put_contents($file_location, "Postituse aeg\tOstusoov\tNimi\tTelefon\tEmail\n", FILE_APPEND);
			}
			file_put_contents($file_location, "$date\t$request\t$name\t$tel\t$email\n", FILE_APPEND);
		}

		return $skip_mail;
	}

	public function get_file_location($contact_form)
	{
		$upload_dir = wp_upload_dir();
		if ($upload_dir['error'])
			return '';

		$name = $upload_dir['basedir'] . '/ws-wpcf7-files';
		// Check if our subfolder exists
		if ( ! is_dir($name)) {
			if ( ! mkdir($name, 0775))
				return '';
			file_put_contents($name . '/.htaccess', 'Deny from all' . PHP_EOL);
		}

		$form_name = $contact_form->pref('ws_save_custom_file');
		if ( ! $form_name)
			$form_name = $contact_form->name();

		$name .= "/{$form_name}-results.tsv";

		return $name;
	}
}
