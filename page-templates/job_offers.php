<?php /* Template Name: All job offers display page */ ?>

<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
    <title><?php echo wp_title( '', true, 'left' ); ?></title>
    

    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-jobs">

    <?php get_template_part('blocks/b_header'); ?>

    <div class="container">
        <div class="content-divider">
            <div class="row">
                <div class="col-sm-4 col-md-3 hidden-xs">

                    <?php get_template_part('blocks/b_sidebar_primary'); ?>
                    
                </div>
                <div class="col-sm-8 col-md-9 print-col-12 padding-left">

                    <?php get_template_part('blocks/b_breadcrumbs'); ?>
                    
                    <h1 class="primary-heading">
                        <?php echo get_the_title(); ?>
                    </h1>

                    <div class="user-added-content">
                        <?php
                            while ( have_posts() ) : the_post();
                            the_content();
                            endwhile;
                        ?>
                    </div>

                    <?php
                        $args=array(
                          'post_type' => 'job_offers',
                          'post_status' => 'publish',
                          'posts_per_page' => -1,
                        );

                        $jobsQuery = new WP_Query($args);
                        if( $jobsQuery->have_posts() ) { ?>
                            <ul class="jobs">
                                <?php
                                while ($jobsQuery->have_posts()) : $jobsQuery->the_post(); ?>
                                    <li class="jobs-item">
                                        <div class="employ">
                                            <div class="employ-date">
                                                <?php 
                                                    $fields = get_fields(get_the_ID());
                                                    if ($fields) { ?>
                                                        <ul class="employ-date-list text-left">
                                                            <li class="employ-date-item"><?php echo pll__('Added at') . ': ' . get_the_date('d.m.Y'); ?></li>
                                                            <?php if ($fields['due_date']){ ?>
                                                                <li class="employ-date-item"><?php echo pll__('Due date') . ': ' . $fields['due_date']; ?></li>
                                                            <?php } ?>

                                                            <?php $locations = wp_get_post_terms( get_the_ID(), 'job_location'); ?>
                                                            <?php if ($locations){
                                                                $locationsList = '';
                                                                // add label
                                                                $locationsList .= pll__('Location') . ': ';
                                                                
                                                                foreach ($locations as $key => $val) {
                                                                    if ($key == 0) {
                                                                        // add just name for first item
                                                                        $locationsList .= $val->name;
                                                                    } else{
                                                                        // prepend coma for rest
                                                                        $locationsList .= ', ' . $val->name;
                                                                    }
                                                                }
                                                                echo '<li class="employ-date-item">' . $locationsList . '</li>';
                                                            } ?>
                                                        </ul>
                                                    <?php } ?>
                                            </div>
                                            <h3 class="employ-secondary-heading">
                                                <a class="employ-secondary-heading-link" href="<?php echo get_permalink(); ?>"><?php echo get_the_title(); ?></a>
                                            </h3>
                                        </div>
                                    </li>
                                <?php
                                endwhile; ?>
                            </ul>
                        <?php
                        }
                    wp_reset_query();  // Restore global post data stomped by the_post().
                    ?>


                </div>
            </div>
        </div>
    </div>
    

    <?php get_template_part('blocks/b_mob_social_icons'); ?>

    <?php get_template_part('blocks/b_footer'); ?> 

    <?php get_template_part('blocks/b_javascripts'); ?> 

</body>
</html>
