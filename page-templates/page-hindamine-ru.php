<?php 
 /* Template Name: Hindamine RU */
?>

<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
    <title><?php echo wp_title( '', true, 'left' ); ?></title>


    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-vahendus page-hindamine-ru">
	<?php get_template_part('blocks/b_header'); ?>

	<div class="container container-medium pb-50">
		<div class="row">
			<div class="col-12">
				<h1 class="primary-heading text-center mb-30"><?php echo get_the_title(); ?></h1>

				<?php 
					$topBtnText = get_field('page-top-button-text');
					$topBtnUrl = get_field('page-top-button-url');
				?>

				<?php if ($topBtnText && $topBtnUrl): ?>
					<div class="text-center pb-40">
						<a href="<?php echo $topBtnUrl; ?>" class="main-button"><?php echo $topBtnText; ?></a>
					</div>
				<?php endif; ?>

				<div class="user-added-content">
					<?php the_content(); ?>
				</div>

				<?php $contactBtnsTitle = get_field('contact-buttons-title'); ?>
				<div class="text-center user-added-content pt-20">
				
				<?php $contactUrl = get_field('contact-buttons-url'); ?>
				<?php if ($contactUrl): ?>
					<h3><a href="<?php echo $contactUrl; ?>" style="font-size: 24px"><?php echo $contactBtnsTitle; ?></a></h3>
				<?php else: ?>
					<h3><?php echo $contactBtnsTitle; ?></h3>
				<?php endif; ?>

					<?php 
						$btn1Text = get_field('button-1-text');
						$btn2Text = get_field('button-2-text');
						$btn1Url = get_field('button-1-url');
						$btn2Url = get_field('button-2-url');
					?>
					<div class="pt-20 mb-30 contact-buttons">
						<?php if ($btn1Text && $btn1Url): ?>
							<a href="<?php echo $btn1Url; ?>" class="main-button"><?php echo $btn1Text; ?></a>
						<?php endif; ?>
						<?php if ($btn2Text && $btn2Url): ?>
							<a href="<?php echo $btn2Url; ?>" class="main-button"><?php echo $btn2Text; ?></a>
						<?php endif; ?>
					</div>
				</div>

				<?php $brokersTitle = get_field('brokers-title'); ?>
				<?php if ($brokersTitle): ?>
					<div class="user-added-content text-center">
						<h3><?php echo $brokersTitle; ?></h3>
					</div>
				<?php endif; ?>

				<?php $brokers = get_field('broker-list'); ?>
				<?php if ($brokers): ?>
				<div class="row flex-row flex-center pt-30 pb-30">
					<?php foreach ($brokers as $brokerId): ?>
						<div class="col-12 col-xs-12 col-sm-6 text-center">
							<?php $contact = getContact([
								'contactsType' => 'postID',
								'pageID' => $brokerId
							]); ?>
							<?php include( locate_template( 'blocks/broker-contact.php', false, false ) ); ?>
						</div>
					<?php endforeach; ?>
				</div>
				<?php endif; ?>

			</div>
		</div>
	</div>

	<?php get_template_part('blocks/b_mob_social_icons'); ?>

	<?php get_template_part('blocks/b_footer'); ?>

	<?php get_template_part('blocks/b_javascripts'); ?>
</body>

</html>