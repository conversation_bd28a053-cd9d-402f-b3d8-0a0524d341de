<?php /* Template Name: Contacts template */ ?>

<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
    <title><?php echo wp_title( '', true, 'left' ); ?></title>


    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-contacts">

    <?php get_template_part('blocks/b_header'); ?>

    <div class="container">
        <div class="content-divider">
            <div class="row">
                <div class="col-sm-4 col-md-3 hidden-xs">

                    <?php get_template_part('blocks/b_sidebar_children_pages'); ?>

                </div>
                <div class="col-sm-8 col-md-9 print-col-12 padding-left">

                    <?php get_template_part('blocks/b_breadcrumbs'); ?>

                    <div class="main-contact">

                        <div class="main-contact-company">
                            <div class="company">
                                <div class="row flex-row">
                                    <div class="col-xs-12 col-md-6 col-flex col-flex-bordered">
                                        <div class="company-contacts">
                                            <div class="firm-contact">
                                                <h1 class="firm-contact-heading"><?php echo get_the_title(); ?></h1>
                                                <?php
                                                    $contactInfo = get_fields();
                                                    if ($contactInfo['contact_row']) {
                                                        foreach ($contactInfo['contact_row'] as $contactRow) {
                                                            echo '<div class="firm-contact-block ' . ($contactRow['create_space_after_line'] ? 'margin-bottom' : '') . '">';
                                                            if ($contactRow['label'] && $contactRow['value']) {
                                                                echo '<div class="firm-contact-label">' . $contactRow['label'] . '</div>';

                                                                echo '<div class="firm-contact-item">';
                                                                if ($contactRow['field_type'] == 'phone') {
                                                                    $phone = preg_replace('/[^\+0-9]+/', '', $contactRow['value']); // remove everything exept numbers and +
                                                                    echo '<a class="firm-contact-item-link" href="tel:' . $phone . '" class="firm-contact-link">' . $contactRow['value'] . '</a>';
                                                                } elseif ($contactRow['field_type'] == 'email') {
                                                                    echo '<a class="firm-contact-item-link" href="mailto:' . $contactRow['value'] . '" class="firm-contact-link">' . $contactRow['value'] . '</a>';
                                                                } else{
                                                                    echo $contactRow['value'];
                                                                }
                                                                echo '</div>';
                                                            }
                                                            echo '</div>';
                                                        }
                                                    }
                                                ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xs-12 col-md-6 col-flex">
                                        <div class="company-contacts">
                                            <div class="firm-contact">
                                                <h1 class="firm-contact-heading"><?php echo get_field('contact_row_2_heading') ?></h1>
                                                <?php
                                                    $contactInfo_2 = get_fields();
                                                    if ($contactInfo_2['contact_row_2']) {
                                                        foreach ($contactInfo_2['contact_row_2'] as $contactRow) {
                                                            echo '<div class="firm-contact-block ' . ($contactRow['create_space_after_line'] ? 'margin-bottom' : '') . '">';
                                                            if ($contactRow['label'] && $contactRow['value']) {
                                                                echo '<div class="firm-contact-label">' . $contactRow['label'] . '</div>';

                                                                echo '<div class="firm-contact-item">';
                                                                if ($contactRow['field_type'] == 'phone') {
                                                                    $phone = preg_replace('/[^\+0-9]+/', '', $contactRow['value']); // remove everything exept numbers and +
                                                                    echo '<a class="firm-contact-item-link" href="tel:' . $phone . '" class="firm-contact-link">' . $contactRow['value'] . '</a>';
                                                                } elseif ($contactRow['field_type'] == 'email') {
                                                                    echo '<a class="firm-contact-item-link" href="mailto:' . $contactRow['value'] . '" class="firm-contact-link">' . $contactRow['value'] . '</a>';
                                                                } else{
                                                                    echo $contactRow['value'];
                                                                }
                                                                echo '</div>';
                                                            }
                                                            echo '</div>';
                                                        }
                                                    }
                                                ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xs-12 col-md-6 col-flex">
                                        <div class="company-person flex-center hidden-xs">
                                            <?php
                                                $args = array(
                                                    'title' => get_field('daily_contact_person_title'),
                                                    'contactsType' => 'field',
                                                    'customFieldsName' => 'daily_contact_person',
                                                    'pageID' => get_the_ID(),
                                                );
                                                $contact = getContact($args);

                                                $renderArgs = array(
                                                    'gaCategory' => 'Kontaktileht',
                                                    'thumbnailClass' => 'halfed-contact-thumbnail',
                                                    'infoClass' => 'halfed-contact-info',
                                                    'singleButtonClass' => 'hidden',
                                                );
                                                renderContact($contact, $renderArgs);
                                            ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h1 class="primary-heading js-scrollable-heading"><?php pll_e('Find our closest Pindi office near you!'); ?></h1>


                        <div class="main-contact-location">

                            <div class="location">
                                <?php
                                $frontpage_id = get_option('page_on_front');
                                $offices = array(
                                    array(
                                        'officeName' => 'tallinn',
                                        'tooltip' => 'Tallinn',
                                        'county' => 'harjumaa',
                                        'page' => get_field('tallinn_contact_page', $frontpage_id),
                                    ),
                                    array(
                                        'officeName' => 'haapsalu',
                                        'tooltip' => 'Haapsalu',
                                        'county' => 'laanemaa',
                                        'page' => get_field('haapsalu_contact_page', $frontpage_id),
                                    ),
                                    array(
                                        'officeName' => 'jogevamaa',
                                        'tooltip' => 'Jõgevamaa',
                                        'county' => 'jogevamaa',
                                        'page' => get_field('jogevamaa_contact_page', $frontpage_id),
                                    ),
                                    array(
                                        'officeName' => 'johvi',
                                        'tooltip' => 'Jõhvi',
                                        'county' => 'idaVirumaa',
                                        'page' => get_field('johvi_contact_page', $frontpage_id),
                                    ),
                                    array(
                                        'officeName' => 'kuressaare',
                                        'tooltip' => 'Kuressaare',
                                        'county' => 'saaremaa',
                                        'page' => get_field('kuressaare_contact_page', $frontpage_id),
                                    ),
                                    array(
                                        'officeName' => 'hiiumaa',
                                        'tooltip' => 'Hiiumaa',
                                        'county' => 'hiiumaa',
                                        'page' => get_field('hiiumaa_contact_page', $frontpage_id),
                                    ),
                                    array(
                                        'officeName' => 'narva',
                                        'tooltip' => 'Narva',
                                        'county' => 'idaVirumaa',
                                        'page' => get_field('narva_contact_page', $frontpage_id),
                                    ),
                                    array(
                                        'officeName' => 'paide',
                                        'tooltip' => 'Paide',
                                        'county' => 'jarvamaa',
                                        'page' => get_field('paide_contact_page', $frontpage_id),
                                    ),
                                    array(
                                        'officeName' => 'polvamaa',
                                        'tooltip' => 'Põlvamaa',
                                        'county' => 'polvamaa',
                                        'page' => get_field('polvamaa_contact_page', $frontpage_id),
                                    ),
                                    array(
                                        'officeName' => 'parnu',
                                        'tooltip' => 'Pärnu',
                                        'county' => 'parnumaa',
                                        'page' => get_field('parnu_contact_page', $frontpage_id),
                                    ),
                                    array(
                                        'officeName' => 'rakvere',
                                        'tooltip' => 'Rakvere',
                                        'county' => 'laaneVirumaa',
                                        'page' => get_field('rakvere_contact_page', $frontpage_id),
                                    ),
                                    array(
                                        'officeName' => 'rapla',
                                        'tooltip' => 'Rapla',
                                        'county' => 'raplamaa',
                                        'page' => get_field('rapla_contact_page', $frontpage_id),
                                    ),
                                    array(
                                        'officeName' => 'tapa',
                                        'tooltip' => 'Tapa',
                                        'county' => 'laaneVirumaa',
                                        'page' => get_field('tapa_contact_page', $frontpage_id),
                                    ),
                                    array(
                                        'officeName' => 'tartu',
                                        'tooltip' => 'Tartu',
                                        'county' => 'tartumaa',
                                        'page' => get_field('tartu_contact_page', $frontpage_id),
                                    ),
                                    array(
                                        'officeName' => 'turi',
                                        'tooltip' => 'Türi',
                                        'county' => 'jarvamaa',
                                        'page' => get_field('turi_contact_page', $frontpage_id),
                                    ),
                                    array(
                                        'officeName' => 'valga',
                                        'tooltip' => 'Valga',
                                        'county' => 'valgamaa',
                                        'page' => get_field('valga_contact_page', $frontpage_id),
                                    ),
                                    array(
                                        'officeName' => 'viljandi',
                                        'tooltip' => 'Viljandi',
                                        'county' => 'viljandimaa',
                                        'page' => get_field('viljandi_contact_page', $frontpage_id),
                                    ),
                                    array(
                                        'officeName' => 'voru',
                                        'tooltip' => 'Võru',
                                        'county' => 'vorumaa',
                                        'page' => get_field('voru_contact_page', $frontpage_id),
                                    ),
                                );
                                ?>
                                <div class="location-map">
                                    <div class="map-wrapper" style="position: relative;">
                                        <img src="<?php echo get_template_directory_uri(); ?>/assets/img/map_estonia.svg?ver=2" usemap="#estoniamap" class="estonia-map-lg" id="estonia-map" />
                                        <map name="estoniamap">
                                            <area class="em-county" identifier="hiiumaa" name="hiiumaa" shape="poly" href="#" coords="58.5,171,70.5,171,82.5,155.5,110,146,109,137.5,103,131.5,100.5,119.5,97,113.5,86,110,84.2,105,76.5,101.5,62,101.5,52.5,107.5,44,117,31,119.5,17.7,119.5,19,131.5,28.5,137.5,46.5,137.5,50,167.5" />
                                            <area class="em-county" identifier="saaremaa" name="saaremaa" shape="poly" href="#" coords="20,293.5,37,279,46.5,251.5,74,248,98,233.5,118.5,224,136.5,206,142.5,194,134,179.5,127,161.5,112.5,161.5,100.5,173.5,91,179.5,67,179.5,61,176,52.5,177,46.5,185.5,34.5,188,25,195,16.5,192.7,4.5,195,0,213,0,237,10.5,249,14,269.5,10.5,291" />
                                            <area class="em-county" identifier="harjumaa" name="harjumaa" shape="poly" href="#" coords="296,117,290,117,289,111,284,110,283,104,277,104,277,99,272,98,269.8,92,265,93,265,80,236,81,235,87,235,98.5,218,99,217,105,211,105,212,111,199,111,199,104,199,92,176,93,175,86,169,86,169,80,163,80,164,63,193,45,214.5,33,217,12.5,245.8,12.5,271,5.5,302,5.5,340.5,1.8,344,17.5,343,50,350,51,349,69,326,69,326,75,313,75,314,88,313,93,307,93,308,104,302,105,302,116" />
                                            <area class="em-county" identifier="polvamaa" name="polvamaa" shape="poly" href="#" coords="488,242,496.5,250,502.5,267,514.5,287.5,520.5,302,512,305.5,496.5,304,494,302.9,493,296,476,296,475,285,458,284,457,279,452,279,451,284,409,284,410,255,415,254,415,248,433,248,434,242" />
                                            <area class="em-county" identifier="vorumaa" name="vorumaa" shape="poly" href="#" coords="494,302.9,493,296,476,296,475,285,458,284,457,279,452,279,451,284,439,284,409,284,391,285,392,290,391,296,392,302,397,302,397,308,403,308,403,314,398,314,397,351.8,434,350,436.5,340,442.5,340.3,446,351.8,472.5,351.8,487,336.3,494,320" />
                                            <area class="em-county" identifier="valgamaa" name="valgamaa" shape="poly" href="#" coords="397,344,397,321,398,314,403,314,403,308,397,308,397,302,392,302,391,296,392,290,391,285,409,284,410,255,398,254,385,255,385,260,349,260,349,255,343,255,343,261,338,261,337,266,331,266,331,272,326,272,325,278,320,278,319,285,341.5,303,356,305.5,365.5,323.5,388.5,349,397,351.8" />
                                            <area class="em-county" identifier="viljandimaa" name="viljandimaa" shape="poly" href="#" coords="319,285,320,278,325,278,326,272,331,272,331,266,337,266,338,261,343,261,343,255,349,255,349,260,361,260,362,243,355,243,355,231,344,230,343,225,355,224,355,218,355,213,361,212,366.1,213.6,370.5,212,373,210.8,373,201,368,200,368,195,362,195,361,189,356,189,355,183,352.5,182,337,182,337.5,170,320,171,319,176,289,177,289,188,283,188,284,194,272,194,271,224,283,224,283,230,289,230,289,248,278,249,276.7,256.5,277,280" />
                                            <area class="em-county" identifier="parnumaa" name="parnumaa" shape="poly" href="#" coords="151,203.5,165.5,231,172.5,260,194,273,215.5,302,226.5,303,235,297,232.5,287.5,251.8,286.3,256.5,281.5,262.5,287.5,277,280,276.7,256.5,278,249,289,248,289,230,283,230,283,224,271,224,271,200,272,194,284,194,283,188,289,188,289,177,290,177,295,177,295,164,266,164,265,171,259,170,259,158.5,241,158,241,164,224,164,223,170,182,170,181,176,176,177,175,188,149.8,189" />
                                            <area class="em-county" identifier="laanemaa" name="laanemaa" shape="poly" href="#" coords="110,102.5,130.5,102.5,139,94,134,83.5,137.5,71.5,164,64,163,80,169,80,169,86,175,86,176,93,199,92,199,111,193,111,194,134,200,134,199,140,194,140,194,152,200,152,199,170,182,170,181,176,176,177,175,188,149.8,189,142.5,183,137.8,170,142.5,159,155,155.5,153,148,140,155.5,129,142,136.5,137.5,131.5,128,121,124.8,110,124,106.5,107.5" />
                                            <area class="em-county" identifier="raplamaa" name="raplamaa" shape="poly" href="#" coords="193,128,193,112.3,193,111,199,111,212,111,211,105,217,105,218,99,235,98.5,236,81,265,80,265,93,269.8,92,272,98,277,99,277,104,283,104,284,110,289,111,290,117,296,117,295,128,290,129,290,140,295,141,295,146,289,147,289,164,266,164,265,171,259,170,259,158.5,241,158,241,164,224,164,223,170,199,170,199,164,200,152,194,152,194,140,199,140,200,134,194,134" />
                                            <area class="em-county" identifier="jarvamaa" name="jarvamaa" shape="poly" href="#" coords="313,75,314,88,313,93,307,93,308,104,302,105,302,116,296,117,295,128,290,129,290,140,295,141,295,146,289,147,289,164,295,164,295,177,319,176,320,171,326,171,337.5,170,338,159,343,158,343,153,355,152,356,141,367,140,368,135,367,117,362,117,361,113.5,362,105,349,105,350,93,355,92,356,87,349,87,349,81,319,79.2,319,75" />
                                            <area class="em-county" identifier="laaneVirumaa" name="laaneVirumaa" shape="poly" href="#" coords="344,17.5,343,32,343,50,350,51,349,69,326,69,326,75,319,75,319,79.2,325,80,349,81,349,87,356,87,355,92,350,93,349,105,362,105,361,113.5,362,117,367,117,368,135,373,135,373,129,385,128,385,134,392,134,392,128,409,128,409,123,421,122,422,93,433,92,434,81,428,80,427,77.5,427,75,421,74,421,57,415,56,416,23.5,415,20,393,17.5,362,11.5" />
                                            <area class="em-county" identifier="jogevamaa" name="jogevamaa" shape="poly" href="#" coords="452,161.5,445,155.5,439,152,439,135,421,134,421,122,409,123,409,128,392,128,392,134,385,134,385,128,373,129,373,135,368,135,367,140,356,141,355,147,355,152,343,153,343,158,338,159,337,182,352.5,182,355,183,356,189,361,189,362,195,379,195,391,194,392,189,411.5,189,427,188,427,177,451,176" />
                                            <area class="em-county" identifier="idaVirumaa" name="idaVirumaa" shape="poly" href="#" coords="547,56,535,68,526.5,74,524,89.5,517,95.5,512,110,464,111,463,116,451,117,451,122,445,123,445,128,439,129,439,135,421,134,421,119.5,422,93,433,92,434,81,428,80,427,77.5,427,75,421,74,421,57,415,56,415,50,416,23.5,440,35.5,466.5,35.5,506,41.5,535,35.5,547,44" />
                                            <area class="em-county" identifier="tartumaa" name="tartumaa" shape="poly" href="#" coords="490.5,238,482,236,481,212,475,212,475,206,463,206,463,182,463,176,458,176,457,165,451.8,164,451,176,427,177,427,188,392,189,391,194,368,195,368,200,373,201,373,210.8,367.8,213,367.8,224,367,236,367,242,362,243,361,260,385,260,385,255,398,254,410,255,415,254,415,248,433,248,434,242,488,242" />
                                        </map>
                                        <ul class="em-tags" style="list-style: none;">
                                            <?php
                                            foreach ($offices as $office) {
                                                echo '<li class="em-tag tags tags-' . $office['officeName'] . '" identifier="' . $office['officeName'] . '" county-identifier="' . $office['county'] . '">';
                                                echo '<div class="em-tooltip">' . $office['tooltip'] . '</div>';
                                                echo '<span class="tags tags-inner"></span>';
                                                echo '</li>';
                                            }
                                            ?>
                                        </ul>
                                        <div class="visible-xs" style="position: absolute; top: 0; right: 0; bottom: 0; left: 0;">
                                        </div>
                                    </div>
                                    <!--<div class="visible-xs em-tags-mob mob-map-controls">
                                        <span class="mob-map-button mob-map-button-prev em-prev"></span>
                                        <ul class="mob-map-controls-list" style="list-style: none;">
                                            <?php
                                            foreach ($offices as $office) {
                                                echo '<li class="em-tag-mob" identifier="' . $office['officeName'] . '">';
                                                echo $office['tooltip'];
                                                echo '</li>';
                                            }
                                            ?>
                                        </ul>
                                        <span class="mob-map-button mob-map-button-next em-next"></span>
                                    </div>-->

                                    <ul class="js-mob-map-slider visible-xs text-center" style="list-style: none;">
                                        <?php
                                        foreach ($offices as $office) {
                                            echo '<li class="em-tag-mob" identifier="' . $office['officeName'] . '">';
                                            echo $office['tooltip'];
                                            echo '</li>';
                                        }
                                        ?>
                                    </ul>
                                </div>

                                <?php
                                $officeContacts = array();
                                foreach ($offices as $office) {
                                    $officeId = '';
                                    if ($office['page']) {
                                        $officeId = $office['page']->ID;
                                    }
                                    $contact = array(
                                        'name' => $office['officeName'],
                                        'id' => $officeId,
                                    );
                                    array_push($officeContacts, $contact);
                                }
                                ?>

                                <?php
                                if ($officeContacts) {
                                    echo '<div class="location-contact-wrap">';
                                    $i = 0;
                                    foreach ($officeContacts as $officeContact) {
                                        $contactInfo = get_field('contact_row', $officeContact['id']);
                                        // print_r($contactInfo);

                                        echo '<div class="em-content location-contact-single ' . ($i == 0 ? 'active' : '') . '" identifier="' . $officeContact['name'] . '">'; ?>
                                            <div class="location-contact">
                                                <div class="firm-contact">
                                                    <?php
                                                    if ($contactInfo) {
                                                        $isFirstBlock = true;
                                                        $isFirstBlockEnd = true;
                                                        $isNewBlock = true;
                                                        $i = 1;
                                                        $itemCount = count($contactInfo);
                                                        foreach ($contactInfo as $infoRow) {
                                                            // decide when to start block
                                                            if ($isFirstBlock) {
                                                                echo '<div class="row firm-contact-primary">';
                                                                echo '<div class="col-sm-6 margin-bottom">';
                                                                echo '<h2 class="firm-contact-heading firm-contact-heading-sm txt-center-xs">' . get_the_title($officeContact['id']) . '</h2>';
                                                                $isFirstBlock = false;
                                                                $isNewBlock = false;
                                                            } elseif ($isNewBlock) {
                                                                echo '<div class="row margin-bottom">';
                                                                $isNewBlock = false;
                                                            }

                                                            // items
                                                            echo '<div class="firm-contact-block ' . (!$isFirstBlockEnd ? 'col-sm-6' : '') . '">';
                                                            if ($infoRow['label'] && $infoRow['value']) {
                                                                echo '<div class="firm-contact-label">' . $infoRow['label'] . '</div>';

                                                                echo '<div class="firm-contact-item">';
                                                                if ($infoRow['field_type'] == 'phone') {
                                                                    $phone = preg_replace('/[^\+0-9]+/', '', $infoRow['value']); // remove everything exept numbers and +
                                                                    echo '<a href="tel:' . $phone . '" class="firm-contact-link">' . $infoRow['value'] . '</a>';
                                                                } elseif ($infoRow['field_type'] == 'email') {
                                                                    echo '<a href="mailto:' . $infoRow['value'] . '" class="firm-contact-link">' . $infoRow['value'] . '</a>';
                                                                } else{
                                                                    echo $infoRow['value'];
                                                                }
                                                                echo '</div>';
                                                            }
                                                            echo '</div>';

                                                            // check if has more items
                                                            if ($i == $itemCount && !$infoRow['create_space_after_line'] && !$isFirstBlockEnd) {
                                                                echo '</div>';
                                                            }

                                                            // decide when to end block
                                                            if ($i != $itemCount && $infoRow['create_space_after_line'] && $isFirstBlockEnd) {
                                                                echo '</div>';
                                                                echo '</div>';
                                                                echo '<div class="row firm-contact-secondary">';
                                                                echo '<div class="col-sm-12 col-md-8">';
                                                                $isNewBlock = true;
                                                                $isFirstBlockEnd = false;
                                                            } elseif($infoRow['create_space_after_line']){
                                                                echo '</div>';
                                                                $isNewBlock = true;
                                                            }
                                                            $i++;
                                                        }
                                                        echo '</div>';
                                                        echo '</div>';
                                                    }
                                                    ?>
                                                </div>
                                            </div>

                                            <div class="location-links">
                                                <div class="row">
                                                    <?php
                                                        // get_field(, $officeContact['id']);
                                                        $officeContact['id'];
                                                        $args = array(
                                                            'title' => get_field($officeContact['name'] . '_title'),
                                                            'contactsType' => 'field',
                                                            'customFieldsName' => 'daily_contact_person',
                                                            'pageID' => $officeContact['id'],
                                                        );
                                                        $contact = getContact($args);
                                                    ?>
                                                    <div class="js-contact-links col-sm-4 txt-center-xs txt-right-med">
                                                        <div class="make-contact js-make-contact">
                                                            <div class="make-contact-lightbox js-close-make-contact">
                                                            </div>
                                                            <div class="make-contact-box mailto">
                                                                <div class="mailto-heading">
                                                                    <div class="gallery-close-button mailto-close-button js-close-make-contact"></div>
                                                                    <h4 class="mailto-heading-header"><?php pll_e('Contact us') ?></h4>
                                                                </div>
                                                                <div class="mailto-content clearfix">
                                                                    <div class="mailto-content-contact">
                                                                        <div class="contact">
                                                                            <div class="contact-thumbnail contact-thumbnail-block mailto-contact-thumbnail">
                                                                                <?php if ($contact['thumbnail']): ?>
                                                                                    <img src="<?php echo $contact['thumbnail']['sizes']['medium']; ?>" alt="<?php echo $contact['thumbnail']['alt']; ?>" class="contact-thumbnail-img">
                                                                                <?php else: ?>
                                                                                    <img src="<?php echo get_template_directory_uri(); ?>/assets/img/no_pic_placeholder.png" alt="No image available" class="contact-thumbnail-img">
                                                                                <?php endif; ?>
                                                                            </div>
                                                                            <div class="contact-info contact-info-block">
                                                                                <?php
                                                                                    if ($contact['name']) {
                                                                                        echo '<div class="contact-info-name">' . $contact['name'] . '</div>';
                                                                                    }

                                                                                    if ($contact['occupation']){
                                                                                        echo '<div class="contact-info-title">' . $contact['occupation'] . '</div>';
                                                                                    }

                                                                                    if ($contact['languages']){
                                                                                        echo '<div class="contact-languages">';
                                                                                        echo '<div class="flags">';
                                                                                        echo '<ul class="flags-list">';
                                                                                        foreach ($contact['languages'] as $lang) {
                                                                                            echo '<li class="flag-item flag-' . $lang . '"></li>';
                                                                                        }
                                                                                        echo '</ul>';
                                                                                        echo '</div>';
                                                                                        echo '</div>';
                                                                                    }
                                                                                ?>
                                                                                <ul class="contact-details">
                                                                                    <?php if ($contact['phone']): ?>
                                                                                        <li class="contact-details-item">
                                                                                            <!-- <?php pll_e('Phone'); ?>: -->
                                                                                            <a class="contact-details-item-link" href="tel:<?php echo $contact['phone']; ?>">
                                                                                                <?php echo $contact['phone'] ?></li>
                                                                                            </a>
                                                                                    <?php endif; ?>
                                                                                    <?php if ($contact['mobile']): ?>
                                                                                        <li class="contact-details-item">
                                                                                            <!-- <?php pll_e('Mobile'); ?>: -->
                                                                                            <a class="contact-details-item-link" href="tel:<?php echo $contact['mobile']; ?>">
                                                                                                <?php echo $contact['mobile'] ?></li>
                                                                                            </a>
                                                                                    <?php endif; ?>
                                                                                    <?php if ($contact['email']): ?>
                                                                                        <li class="contact-details-item">
                                                                                            <!-- <?php pll_e('Email'); ?>: -->
                                                                                            <a class="contact-details-item-link" href="mailto:<?php echo $contact['email']; ?>">
                                                                                                <?php echo $contact['email'] ?>
                                                                                            </a>
                                                                                        </li>
                                                                                    <?php endif; ?>
                                                                                </ul>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                    <form class="js-contact-person-form mailto-content-form" identifier-action="sendMailTakeContact">
                                                                        <div class="js-form-submitted-message mailto-form-after-submit-message">
                                                                        </div>
                                                                        <div class="mailto-form-group js-field">
                                                                            <input name="mailForm[id]" type="text" value="<?php echo $contact['contactID']; ?>" hidden>
                                                                            <label class="mailto-label">
                                                                                <?php pll_e('Name'); ?>:
                                                                            </label>
                                                                            <input name="mailForm[name]" type="text" class="js-input js-required mailto-form-input">
                                                                            <span class='js-error'></span>
                                                                        </div>
                                                                        <div class="mailto-form-group mailto-form-group-last js-field">
                                                                            <label class="mailto-label">
                                                                                <?php pll_e('Email'); ?>:
                                                                            </label>
                                                                            <input name="mailForm[email]" type="text" class="js-input js-required js-email mailto-form-input">
                                                                            <span class='js-error'></span>
                                                                        </div>

                                                                        <div class="mailto-form-group-full js-field">
                                                                            <label class="mailto-label">
                                                                                <?php pll_e('Message content'); ?>:
                                                                            </label>
                                                                            <textarea name="mailForm[message]" class="js-input js-required mailto-form-textarea"></textarea>
                                                                            <span class='js-error'></span>
                                                                        </div>
                                                                        <button class="js-contact-person-form-submit btn main-button mailto-button" type="button"><?php pll_e('Send message'); ?></button>
                                                                    </form>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="js-make-contact-button btn main-button location-links-button">
                                                            <?php pll_e('Take contact') ?>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-4 txt-center-xs">
                                                        <a class="link-button location-links-button location-links-link" href="<?php echo get_permalink($officeContact['id']); ?>">
                                                            <?php pll_e('See all employers'); ?>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php
                                        echo '</div>';
                                    }
                                    echo '</div>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php get_template_part('blocks/b_mob_social_icons'); ?>

    <?php get_template_part('blocks/b_footer'); ?>

    <?php get_template_part('blocks/b_javascripts'); ?>

</body>
</html>
