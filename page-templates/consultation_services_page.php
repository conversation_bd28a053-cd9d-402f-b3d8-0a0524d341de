<?php /* Template Name: Consultation Services template */

$langs = [
    'et' => 'Estonian',
    'en' => 'English',
    'ru' => 'Russian',
];
?>

<!DOCTYPE html>
<!--[if lt IE 9]>
<html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>
<html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-js" lang="et"><!--<![endif]-->
<head>
    <title><?php echo wp_title('', true, 'left'); ?></title>
    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-default">
    <?php get_template_part('blocks/b_header'); ?>

    <div class="container">
        <div class="content-divider">
            <div class="row">
                <div class="col-sm-4 col-md-3 hidden-xs">
                    <?php get_template_part('blocks/b_sidebar_primary'); ?>
                </div>
                <div class="col-sm-8 col-md-9 print-col-12 padding-left">
                    <?php get_template_part('blocks/b_breadcrumbs'); ?>

                    <h1 class="primary-heading"><?php echo get_the_title(); ?></h1>
                    <div class="user-added-content">
                        <?php while (have_posts()) : the_post(); ?>
                            <?php the_content(); ?>
                        <?php endwhile; ?>

                        <?php if (get_field('newsletter_subscription')): ?>
                            <div class="col-md-7 no-gutter">
                                <div class="subscribe">
                                    <form class="js-subscribe-newsletter subscribe-join">
                                        <div class="subscribe-success">
                                            <span class="subscribe-success-text">
                                                <?php pll_e('Successfully joined newsletter'); ?>
                                            </span>
                                        </div>
                                        <h6 class="subscribe-title">
                                            <?php the_field('newsletter_subscription'); ?>
                                        </h6>

                                        <span class="subscribe-info">
                                            <?php pll_e('Front page subscription intro'); ?>
                                        </span>

                                        <input class="js-field-language hidden"
                                               value="<?php echo $langs[pll_current_language()]; ?>">
                                        <input class="js-field-email subscribe-join-input"
                                               placeholder="<?php pll_e('Insert email address'); ?>">

                                        <div class="subscribe-privacy">
                                            <input type="checkbox" name="checkbox" value="check"
                                                   class="privacy-checkbox js-privacy-checkbox" />
                                            <a href="/privaatsuspoliitika/"
                                               target="_blank"><?php pll_e('Newsletter privacy agreement'); ?></a>
                                        </div>

                                        <div class="subscribe-privacy-error">
                                            <span class="subscribe-error-text"><?php pll_e('Newsletter privacy agreement error'); ?></span>
                                        </div>

                                        <div class="subscribe-email-error">
                                            <span class="subscribe-error-text"><?php pll_e('Newsletter field error'); ?></span>
                                        </div>

                                        <button type="submit" class="main-button main-button-larger">
                                            <?php pll_e('Join'); ?>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <h2 class="secondary-heading-w-border"><?php pll_e('Consultation Form Title'); ?></h2>

                    <div class="row">
                        <div class="col-xl-4 col-md-6 col-sm-8">
                            <form class="js-wp-ajax-form" action="" method="POST">
                                <input type="hidden" name="action" value="consultation_form_submit">

                                <div class="nna-dropdown dropdown-filter dropdown">
                                    <input type="hidden" name="asset_type" value="<?php pll_e('House'); ?>">
                                    <label class="filter-label"><?php pll_e('Asset Type'); ?>:</label>

                                    <button
                                            id="asset-type-dropdown"
                                            class="btn nna-dropdown-btn dropdown-filter-btn dropdown-toggle gray"
                                            type="button"
                                            aria-expanded="false"
                                            data-toggle="dropdown"
                                    >
                                        <?php pll_e('Apartment'); ?>
                                        <i class="dropdown-filter-btn-icon"></i>
                                    </button>

                                    <ul class="dropdown-menu filter-dropdown-menu"
                                        aria-labelledby="asset-type-dropdown"
                                    >
                                        <li class="nna-filter-option filter-dropdown-option"
                                            filter-value="<?php pll_e('Apartment'); ?>"><?php pll_e('Apartment'); ?></li>
                                        <li class="nna-filter-option filter-dropdown-option"
                                            filter-value="<?php pll_e('House'); ?>"><?php pll_e('House'); ?></li>
                                        <li class="nna-filter-option filter-dropdown-option"
                                            filter-value="<?php pll_e('Land'); ?>"><?php pll_e('Land'); ?></li>
                                        <li class="nna-filter-option filter-dropdown-option"
                                            filter-value="<?php pll_e('Commercial Place'); ?>"><?php pll_e('Commercial Place'); ?></li>
                                    </ul>
                                </div>

                                <div>
                                    <label class="filter-label"><?php pll_e('Asset Address'); ?>:</label>
                                    <input class="js-input js-required mailto-form-input" type="text" name="asset_address" />
                                </div>

                                <div>
                                    <label class="filter-label text-capitalize"><?php pll_e('Name'); ?>:</label>
                                    <input class="js-input js-required mailto-form-input" type="text" name="customer_name" />
                                </div>

                                <div>
                                    <label class="filter-label"><?php pll_e('Email Field'); ?>:</label>
                                    <input class="js-input js-required mailto-form-input" type="text" name="customer_email" />
                                </div>

                                <div>
                                    <label class="filter-label text-capitalize"><?php pll_e('Phone'); ?>:</label>
                                    <input class="js-input js-required mailto-form-input" type="text" name="customer_phone" />
                                </div>

                                <div>
                                    <label class="filter-label text-capitalize"><?php pll_e('Notes'); ?>:</label>
                                    <textarea class="js-input mailto-form-textarea" name="customer_message"></textarea>
                                </div>

                                <button class="js-wp-ajax-form-submit btn main-button mailto-button" type="submit"><?php pll_e('Send'); ?></button>
                                <div class="js-response"></div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php get_template_part('blocks/b_mob_social_icons'); ?>
    <?php get_template_part('blocks/b_footer'); ?>
    <?php get_template_part('blocks/b_javascripts'); ?>

    <script>
        $(function () {
            $('body').on('submit', '.js-wp-ajax-form', onWpAjaxFormSubmit);
        });

        function onWpAjaxFormSubmit(event) {
            event.preventDefault();

            var $that = $(this);
            $.each($that.find('.js-input'), function (index, element) {
                $(element).removeClass('has-error');

                if ($(element).hasClass('js-required') && !$(element).val()) {
                    $(element).addClass('has-error');
                }
            });

            if ($that.find('.has-error').length) {
                return;
            }

            $.ajax({
                url: nnaAjaxObject.ajaxUrl,
                method: $that.attr('method'),
                data: $that.serialize(),
                success: onWpAjaxFormSuccess,
                error: onWpAjaxFormError,
            });
        }

        function onWpAjaxFormSuccess(response) {
            var $input = $('form input[name="action"][value="' + response.action + '"]');
            var $form = $input.closest('form');

            $form.find('.js-response').text('');
            if (response.status === 'success') {
                $form.html(response.message);
                return;
            }

            $form.find('.js-response').text(response.message);
        }

        function onWpAjaxFormError(error) {
            console.error(error);
        }
    </script>
</body>
</html>
