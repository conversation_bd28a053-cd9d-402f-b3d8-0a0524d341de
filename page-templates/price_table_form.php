<?php
/* Template Name: Price table page */

$custom_fields = get_fields();
$url = "https://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
?>
<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-js" lang="et">
<!--<![endif]-->

<head>
    <title><?php echo wp_title('', true, 'left'); ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
    .grecaptcha-badge {
        display: none !important;
    }
    body img.gform_ajax_spinner {
	    max-width: 50px;
    }

    div#gform_confirmation_message_1 {
        font-size: 24px;
        color: #165527;
        border: 3px solid #ffac00;
        display: inline-flex;
        padding: 10px;
    }

    .form-template-page .user-added-content .gform_wrapper.gravity-theme .gform_fields{
        grid-row-gap: 0px;
    }
    </style>
    <?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-evaluation form-template-page price-template-page type-<?= $custom_fields['form_type'] ?>">

    <?php get_template_part('blocks/b_header'); ?>

    <div class="container">
        <div class="content-divider">
            <div class="row">
                <div class="col-sm-4 col-md-3 hidden-xs">
                    <?php if (strpos($url, 'teenused-erakliendile') == true || strpos($url, 'kinnisvarateenused-arikliendile') == true) {
                        get_template_part('blocks/b_sidebar_children_pages'); 
                        } else {
                         get_template_part('blocks/b_sidebar_primary');
                    }?>
                </div>
                <div class="col-sm-8 col-md-9 print-col-12 padding-left">
                    <?php get_template_part('blocks/b_breadcrumbs') ?>


                    <br><br>

                    <h1 class="primary-heading"><b><?php single_post_title(); ?></b></h1>
                    <br><br>
                    <div class="user-added-content main__content">
                        <div class="user-added-content__left">
                            <?php if( have_rows('package_table') ): ?>
                            <?php while( have_rows('package_table') ): the_row();  ?>
                            <?php the_sub_field('before_form_content'); ?>
                            <?php endwhile; ?>
                            <?php endif; ?>
                        </div>
                    </div>


                    <div class="user-added-content main__content">
                        <div id="contact-form" class="user-added-content__left">
                            <?php $shortcode_output = do_shortcode('[gravityform id="1" title="false" description="false" ajax="true"]'); 
                            echo $shortcode_output; ?>
                        </div>
                    </div>

                   
                <?php if( have_rows('package_table') ): ?>
                    <?php while( have_rows('package_table') ): the_row();  ?>
                    <h2 class="primary-heading"><b><?= get_sub_field('main_heading_before_table'); ?></b></h2>

                    <br>
                    <table class="price-table top-element">
                        <tbody>
                            <tr class="no-style">
                                <td class="hide-first-three-cells" width="55%"></td>
                                <td class="hide-first-three-cells" width="15%"></td>
                                <td class="hide-first-three-cells" width="15%"></td>
                                <td class="hide-first-three-cells" width="15%"></td>
                            </tr>
                            <tr class="price-table-head">
                                <td class="hide-first-three-cells">
                                    <?php
                                        $sectionTitle = get_sub_field('section_title');
                                        $sectionSubtitle = get_sub_field('section_subtitle');
                                    ?>
                                    <h1><?php echo $sectionTitle; ?></h1>
                                    <p><b><?php echo $sectionSubtitle; ?></b></p>
                                </td>

                                <td>
                                    <?php
                                        $image1 = get_sub_field('image_1');
                                        $text1 = get_sub_field('text_1');
                                        ?>
                                    <img height="70" src="<?php echo $image1; ?>" alt="">
                                    <br>
                                    <br><text><?php echo $text1; ?></text>
                                </td>
                                <td>
                                    <?php
                                        $image2 = get_sub_field('image_2');
                                        $text2 = get_sub_field('text_2');
                                    ?>
                                    <img height="70" src="<?php echo $image2; ?>" alt="">
                                    <br>
                                    <br><text><?php echo $text2; ?></text>
                                </td>
                                <td class="green-width">
                                    <?php
                                        $image3 = get_sub_field('image_3');
                                        $text3 = get_sub_field('text_3');
                                    ?>
                                    <img height="70" src="<?php echo $image3; ?>" alt="">
                                    <br>
                                    <br><text><?php echo $text3; ?></text>
                                </td>
                            </tr>


                            <?php while (have_rows('price_rows')) : the_row(); ?>
                            <?php
                                $rowTitle = get_sub_field('row_title');
                                $rowItem1Icon = get_sub_field('row_item_1_icon');
                                $rowItem2Icon = get_sub_field('row_item_2_icon');
                                $rowItem3Icon = get_sub_field('row_item_3_icon');
                                $rowDescription = get_sub_field('row_description');
                            ?>
                            <tr class="price-row <?php echo (get_row_index() === 1) ? 'active' : ''; ?>">
                                <td>
                                    <a href="#wordpress-asset-updates" class="price-table-help"></a>
                                    <?php echo $rowTitle; ?>
                                </td>
                                <td>
                                    <i class="<?php echo $rowItem1Icon; ?>"></i>
                                </td>
                                <td>
                                    <i class="<?php echo $rowItem2Icon; ?>"></i>
                                </td>
                                <td>
                                    <i class="<?php echo $rowItem3Icon; ?>"></i>
                                </td>
                            </tr>
                            <tr class="price-details <?php echo (get_row_index() === 1) ? 'expanded' : 'collapsed'; ?>">
                                <td colspan="4"><?php echo $rowDescription; ?></td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                <?php endwhile; ?>
                <?php endif; ?>

                    <div class="user-added-content main__content fade-up-slide">
                        <div class="user-added-content__left">
                            <?= get_the_content(); ?>
                        </div>
                    </div>

                    <?php if( have_rows('package_table') ): ?>
                    <?php while( have_rows('package_table') ): the_row();  ?>
                    <div class="user-added-content__right">
                        <div class="js-gallery new-vahendus-page">
                            <?php if (get_sub_field('gallery_images')) :
                                    foreach (get_sub_field('gallery_images') as $image_url) : ?>
                            <div>
                                <img src="<?= $image_url ?>" loading="lazy">
                            </div>
                            <?php endforeach;
                                endif; ?>
                        </div>
                    </div>
                    <?php endwhile; ?>
                    <?php endif; ?>



                    <div class="new-gallery-list popup-new-gallery">
                        <?php if( have_rows('package_table') ): ?>
                        <?php while( have_rows('package_table') ): the_row(); ?>
                        <?php if (get_sub_field('gallery_images')) :
                                    foreach (get_sub_field('gallery_images') as $image_url) : ?>
                        <div class="new-items">
                            <div class="new-gall-imagebox">
                                <img src="<?= $image_url ?>" loading="lazy" class="fade-up-slide">
                            </div>
                        </div>
                        <?php endforeach;
                                endif; ?>
                        <?php endwhile; ?>
                        <?php endif; ?>
                    </div>




                    <br><br><br><br>

                 

                    <div class="user-added-content main__content">
                        <div class="user-added-content__left">
                            <?php if( have_rows('package_table') ): ?>
                            <?php while( have_rows('package_table') ): the_row();  ?>
                            <?php the_sub_field('after_form_content'); ?>
                            <?php endwhile; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <br><br>

                    <?php if ($custom_fields["important_links"]) : ?>
                    <div class="user-added-content links__container">
                        <h3><?= pll__('Olulised lingid') ?></h3>
                        <div>
                            <?php foreach ($custom_fields["important_links"] as $data) : ?>
                            <a href="<?= $data['page']['url'] ?>" target="<?= $data['page']['target'] ?>">
                                <?= $data['page']['title'] ?>
                            </a>
                            <?php endforeach ?>
                        </div>
                    </div>
                    <?php endif ?>


                    <?php if ($custom_fields['form_type'] == 'haldus' && $custom_fields["gallery"]) : ?>
                    <div class="js-mobile-gallery">
                        <?php foreach ($custom_fields["gallery"] as $image_url) : ?>
                        <div>
                            <img src="<?= $image_url ?>" loading="lazy">
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>

                    <?php if ($custom_fields["brokers"]) : ?>
                    <div class="user-added-content brokers__container">
                        <h3><?= pll__('Kliendid usaldavad meid') ?></h3>
                        <div class="js-broker-gallery">
                            <?php foreach ($custom_fields["brokers"] as $data) :
                                    $img_url = get_field('contact_thumbnail_image', $data["broker"]->ID);
                                    $img_url = $img_url ? $img_url['url'] : ''; ?>
                            <a href="<?= $data['link']['url'] ?>" target="<?= $data['link']['target'] ?>">
                                <img src="<?= $img_url ?>" alt="" loading="lazy">
                                <p><?= $data['quote'] ?></p>
                                <span><?= $data["name"] ?></span>
                            </a>
                            <?php endforeach ?>
                        </div>
                    </div>
                    <?php endif ?>

                    <?php if ($custom_fields["icons"]) : ?>
                    <div class="user-added-content icons__container">
                        <?php foreach ($custom_fields["icons"] as $data) : ?>
                        <a href="<?= $data['link']['url'] ?>" target="<?= $data['link']['target'] ?>">
                            <img src="<?= $data['image'] ?>" alt="" loading="lazy">
                            <div><?= $data['link']['title'] ?></div>
                        </a>
                        <?php endforeach ?>
                    </div>
                    <?php endif ?>



                </div>
            </div>
        </div>

        <div class="user-added-content footer__text">
            <?= $custom_fields["footer_textarea"] ?>
        </div>
    </div>

    <?php
    get_template_part('blocks/b_mob_social_icons');
    get_template_part('blocks/b_footer');
    get_template_part('blocks/b_javascripts');
    ?>


    <?php if( have_rows('package_table') ): while( have_rows('package_table') ): the_row();  ?>
    <?php $button_text = get_sub_field('scroll_to_contact_form_button_text'); if ($button_text) : ?>
    <button id="scrollToContactButton" class="button-jumper" onclick="scrollToContactForm()"><?php echo esc_html($button_text); ?></button>
    <?php endif; endwhile; endif; ?>


    <script>
    var button = document.getElementById("scrollToContactButton");
    var contactForm = document.getElementById("contact-form");
    var isButtonVisible = true;

    function scrollToContactForm() {
        var contactForm = document.getElementById("contact-form");
        var formHeight = contactForm.offsetHeight;
        var windowHeight = window.innerHeight;

        var scrollTo = contactForm.offsetTop - (windowHeight - formHeight) / 2;
        window.scrollTo({
            top: scrollTo,
            behavior: 'smooth'
        });
    }

    function handleScroll() {
        var scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
        var contactForm = document.getElementById("contact-form");
        var formHeight = contactForm.offsetHeight;
        var windowHeight = window.innerHeight;
        var button = document.getElementById("scrollToContactButton");

        var hideDistance = 1 * parseFloat(getComputedStyle(document.documentElement).fontSize); // 1 display worth in pixels

        if (scrollPosition > contactForm.offsetTop - windowHeight + hideDistance &&
            scrollPosition < contactForm.offsetTop + formHeight) {
            button.style.display = 'none';
        } else {
            button.style.display = 'block';
        }
    }



    window.addEventListener("scroll", handleScroll);
    </script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Apply the effect to the top element immediately
        var topElement = document.querySelector('.top-element');
        if (topElement) {
            topElement.classList.add('top-element-show');
        }
    });


    function handleScroll() {
        var fadeUpSlideElements = document.querySelectorAll('.fade-up-slide');

        fadeUpSlideElements.forEach(function(element) {
            var elementTop = element.getBoundingClientRect().top;
            var elementBottom = element.getBoundingClientRect().bottom;
            var windowHeight = window.innerHeight;

            if (elementTop < windowHeight / 1 && elementBottom > windowHeight / 1) {
                element.classList.add('fade-up-slide-show');
            }
        });
    }

    window.addEventListener('scroll', handleScroll);
    </script>

</body>

</html>