
// Bug Fixes
// --------------------------------------------------------------------------

// Copyright 2014-2015 Twitter, Inc.
// Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)

if (navigator.userAgent.match(/IEMobile\/10\.0/)) {
  var msViewportStyle = document.createElement('style')
  msViewportStyle.appendChild(
    document.createTextNode(
      '@-ms-viewport{width:auto!important}'
    )
  )
  document.querySelector('head').appendChild(msViewportStyle)
}




// ACF Google map javascript
// --------------------------------------------------------------------------


(function($) {
/*
*  new_map
*
*  This function will render a Google Map onto the selected jQuery element
*
*  @type    function
*  @date    8/11/2013
*  @since   4.3.0
*
*  @param   $el (jQuery element)
*  @return  n/a
*/
function new_map( $el ) {
    // var
    var $markers = $el.find('.marker');
    // vars
    var args = {
        zoom        : 14,
        center      : new google.maps.LatLng(0, 0),
        mapTypeId   : google.maps.MapTypeId.ROADMAP,
        disableDefaultUI: true,
    };
    // create map
    var map = new google.maps.Map( $el[0], args);
    // add a markers reference
    map.markers = [];
    // add markers
    $markers.each(function(){
        add_marker( $(this), map );
    });
    // center map
    center_map( map );

    // return
    return map;
}



/*
*  add_marker
*
*  This function will add a marker to the selected Google Map
*
*  @type    function
*  @date    8/11/2013
*  @since   4.3.0
*
*  @param   $marker (jQuery element)
*  @param   map (Google Map object)
*  @return  n/a
*/
function add_marker( $marker, map ) {
    // var
    var latlng = new google.maps.LatLng( $marker.attr('data-lat'), $marker.attr('data-lng') );
    // create marker
    var marker = new google.maps.Marker({
        position    : latlng,
        map         : map
    });
    // add to array
    map.markers.push( marker );
    // if marker contains HTML, add it to an infoWindow
    if( $marker.html() )
    {
        // create info window
        var infowindow = new google.maps.InfoWindow({
            content     : $marker.html()
        });
        // show info window when marker is clicked
        google.maps.event.addListener(marker, 'click', function() {
            infowindow.open( map, marker );
        });
    }
}

/*
*  center_map
*
*  This function will center the map, showing all markers attached to this map
*
*  @type    function
*  @date    8/11/2013
*  @since   4.3.0
*
*  @param   map (Google Map object)
*  @return  n/a
*/
function center_map( map ) {
    // vars
    var bounds = new google.maps.LatLngBounds();
    // loop through all markers and create bounds
    $.each( map.markers, function( i, marker ){
        var latlng = new google.maps.LatLng( marker.position.lat(), marker.position.lng() );
        bounds.extend( latlng );
    });
    // only 1 marker?
    if( map.markers.length == 1 )
    {
        // set center of map
        map.setCenter( bounds.getCenter() );
        map.setZoom( 15 );
    }
    else
    {
        // fit to bounds
        map.fitBounds( bounds );
    }
}

/*
*  document ready
*
*  This function will render each map when the document is ready (page has loaded)
*
*  @type    function
*  @date    8/11/2013
*  @since   5.0.0
*
*  @param   n/a
*  @return  n/a
*/
// global var
var map = null;
$(document).ready(function(){
    $('.acf-map').each(function(){
        // create map
        map = new_map( $(this) );
    });
});
})(jQuery);

var ua = window.navigator.userAgent;
var msie = ua.indexOf("MSIE ");

var mapUrl = (msie > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./)) ? 'http://www.pindi.ee/wp-content/themes/newtime/assets/img/map_estonia.png?ver=2' : 'http://www.pindi.ee/wp-content/themes/newtime/assets/img/map_estonia.svg?ver=2';
$('#estonia-map').attr('src', mapUrl);

// Main Javascript
// --------------------------------------------------------------------------


$(function (){
    $.DLMenu.prototype._resetMenu = function() {
        this.$menu.removeClass( 'dl-subview' );
        this.$menuitems.removeClass( 'dl-subview dl-subviewopen' );

        var activeItem = this.$menu.find('.current-menu-item');
        if (activeItem) {
            if (this.$menu.find('.current-menu-ancestor').length) {
                this.$menu.addClass('dl-subview');
                var ancestors = [];
                $.each(this.$menu.find('.current-menu-ancestor'), function(i, val){
                    $(val).addClass('dl-subview');
                    ancestors.push(val);
                });
                if (ancestors.length) {
                    $(ancestors).last().removeClass('dl-subview').addClass('dl-subviewopen');
                }
            }
        }
    };
    // Activate dlmenu
    $('#dl-menu').dlmenu();
    $('#dl-menu-footer').dlmenu();
    var instanceMainMenu = $('#dl-menu').data('dlmenu')._resetMenu();
    if ( instanceMainMenu ) {
        instanceMainMenu._resetMenu();
    }
    var instanceFooterMenu = $('#dl-menu-footer').data('dlmenu');
    if ( instanceFooterMenu ) {
        instanceFooterMenu._resetMenu();
    }

    $('body').on('click', '[link-identifier]', function(){
        $('[link-identifier], [content-indentifier]').removeClass('active');
        var identifier = $(this).attr('link-identifier');
        $(this).addClass('active');
        $('[content-indentifier="' + identifier + '"]').addClass('active');
    });

    $('body').on('click', '.js-offer-price-button', function(){
        $('.js-offer-price-send').addClass('active');
        $('.js-offer-price-amount').removeClass('active');
    });

    $('body').on('click', '.js-offer-price-send-button', function(){
        $('.js-offer-price-send').removeClass('active');
        $('.js-offer-price-amount').addClass('active');
    });

    $('body').on('click', '.menu-side-link', function(e){
        if (/^#/.test($(this).attr('href'))) {
            e.preventDefault();
            $(this).toggleClass('active');
        }
    });

    $('body').on('click', '.js-offer-price-favorite', function(){
        e.preventDefault();
    });

    $('body').on('click', '.js-offer-more-map', function(){
        $('.object-regional-contact-map').toggleClass('active');
    });

    $('body').on('click', '.js-offer-more-pics', function(){
        $('.object-regional-contact-map').removeClass('active');
    });

    $('body').on('click', '.js-object-locale-close', function(){
        $('.object-regional-contact-map').removeClass('active');
    });

    $('body').on('click', '.js-image-thumbnail', function(){
        $('.object-regional-contact-map').removeClass('active');
    });

    // slider
    var frontPageSlider = setInterval('cycleImages()', 5000);
    //slider-buttons
    $('body').on('click', '.js-slider-buttons-btn', function(){
        if ($(this).hasClass('disabled') || $(this).hasClass('active')) return;

        $('.js-slider-buttons-btn').addClass('disabled');
        clearInterval(frontPageSlider);
        var slideIdentifier = $(this).attr('button-identifier');
        var $active = $('.js-slider-single.active');
        var $next = $('.js-slider-single[slide-identifier="' + slideIdentifier + '"]');

        var $activeBtn = $('.js-slider-buttons-btn.active');
        var $nextBtn = $('.js-slider-buttons-btn[button-identifier="' + slideIdentifier + '"]');

        $activeBtn.removeClass('active');
        $nextBtn.addClass('active');
        setTimeout(function() {
            $active.removeClass('active');//reset the z-index and unhide the image
            $next.addClass('active');//make the next image the top one
            $('.js-slider-buttons-btn').removeClass('disabled');
        }, 2000);
        frontPageSlider = setInterval('cycleImages()', 5000);
        //$('.js-slider-single').css('background-image', image);
    });

    // gallery thingie
        //initial click
        $('body').on('click', '.js-image-thumbnail', function(){
            $('body').css('overflow', 'hidden');

            $('.js-gallery').addClass('active');
            $('.js-image-wrap').removeClass('active');
            var identifier = $(this).attr('image-identifier');
            var image = $('.js-image[image-identifier="' + identifier + '"]').css('background-image');

            if ($('.js-image-wrap').length > 6) {
                var realIdentifier = parseInt(identifier) + 1;
                var imageWidth = $('.js-image').outerWidth(true) + 4;
                var initialLeft = -1 * imageWidth * identifier;

                $('.js-images-list').css('left', initialLeft);
            };

            $('.js-image[image-identifier="' + identifier + '"]').closest('.js-image-wrap').addClass('active');
            $('.js-main-image').css('background-image', image);
            imagecounter();
        });

        $('body').on('click', '.js-offer-more-pics', function(){
            $('body').css('overflow', 'hidden');

            $('.js-gallery').addClass('active');
            $('.js-image-wrap').removeClass('active');
            var identifier = $('.js-image').attr('image-identifier');
            var image = $('.js-image[image-identifier="' + identifier + '"]').css('background-image');
            $('.js-image[image-identifier="' + identifier + '"]').closest('.js-image-wrap').addClass('active');
            $('.js-main-image').css('background-image', image);
            imagecounter();
        });


        var $wrapper = $('.gallery-container');
        function scrollRight(){
            $wrapper.animate({scrollLeft: '+=100'}, 400, 'linear', scrollRight);
        }

        function scrollLeft(){
            $wrapper.animate({scrollLeft: '-=100'}, 400, 'linear', scrollLeft);
        }

        function stopScrolling(){
            $wrapper.stop();
        }
        $('body').on('mousedown touchstart', '.js-scroll-right', function(){
            scrollRight($wrapper);
        }).on('mouseup touchend', '.js-scroll-right', function(){
            stopScrolling($wrapper);
        }).on('mousedown touchstart', '.js-scroll-left', function(){
            scrollLeft($wrapper);
        }).on('mouseup touchend', '.js-scroll-left', function(){
            stopScrolling($wrapper);
        });
        //$('.js-scroll-left').mousedown(scrollLeft).mouseup(stopScrolling);


        //Buttons
        $('body').on('click', '.js-gallery-btn-next', function(){
                var curimage = $('.js-image-wrap.active');
                var identifier = $('.js-image-wrap.active').find('.js-image').attr('image-identifier');
                var nextIdentifier = parseInt(identifier) + 1;
                var nextImage = $('.js-image[image-identifier="' + nextIdentifier + '"]').css('background-image');


               /* if ($('.js-image-wrap').length > 6) {
                    var imageWidth = $('.js-image').width();
                    var curLeft = $('.js-images-list').css('left');
                    var curLeftPx = parseInt(curLeft) - imageWidth - 4 + "px";
                    $('.js-images-list').css('left', curLeftPx);
                };*/

            if (!$(curimage).is(':last-child')) {
                $('.js-image-wrap').removeClass('active');
                $('.js-image[image-identifier="' + nextIdentifier + '"]').closest('.js-image-wrap').addClass('active');
                $('.js-main-image').css('background-image', nextImage);
            };

            /*if (!$(curimage).next().length) {
                var firstchild = $('.js-image-wrap:first-child');
                var firstchildidentifier = $('.js-image-wrap:first-child').find('.js-image').attr('image-identifier');
                var firstchildImage = $('.js-image[image-identifier="' + firstchildidentifier + '"]').css('background-image');
                $(firstchild).find('.js-image[image-identifier="' + firstchildidentifier + '"]').closest('.js-image-wrap').addClass('active');
                $('.js-main-image').css('background-image', firstchildImage);
            };*/
            imagecounter();
        });

        $('body').on('click', '.js-gallery-btn-prev', function(){
                var curimage = $('.js-image-wrap.active');
                var identifier = $('.js-image-wrap.active').find('.js-image').attr('image-identifier');
                var prevIdentifier = parseInt(identifier) - 1;
                var prevImage = $('.js-image[image-identifier="' + prevIdentifier + '"]').css('background-image');

               /* if ($('.js-image-wrap').length > 6) {
                    var imageWidth = $('.js-image').width();
                    var curLeft = $('.js-images-list').css('left');
                    var curLeftPx = parseInt(curLeft) + imageWidth + 4 + "px";
                    $('.js-images-list').css('left', curLeftPx);
                };*/

            if (!$(curimage).is(':first-child')) {
                $('.js-image-wrap').removeClass('active');
                $('.js-image[image-identifier="' + prevIdentifier + '"]').closest('.js-image-wrap').addClass('active');
                $('.js-main-image').css('background-image', prevImage);
            };


            /*if (!$(curimage).prev().length) {
                var lastchild = $('.js-image-wrap:last-child');
                var lastchildidentifier = $('.js-image-wrap:last-child').find('.js-image').attr('image-identifier');
                var lastchildImage = $('.js-image[image-identifier="' + lastchildidentifier + '"]').css('background-image');
                $(lastchild).find('.js-image[image-identifier="' + lastchildidentifier + '"]').closest('.js-image-wrap').addClass('active');
                $('.js-main-image').css('background-image', lastchildImage);
            };*/
            imagecounter();
        });

        //close click
        $('body').on('click', '.js-close', function(e){
            $('body').css('overflow', 'visible');
            e.stopPropagation();
            $('.js-gallery').removeClass('active');
        });

        //keys
        $('body').keyup(function(e) {
            if (e.keyCode == 27){
                $('.js-gallery').removeClass('active');
                $('body').css('overflow', 'visible');
                $('.js-images-list').css('left' , '0');
            }

            if (e.keyCode == 39) {
                    $( '.js-gallery-btn-next' ).click();
                    $('.js-gallery-btn-next').addClass('active');
                    // var curimage = $('.js-image-wrap.active');
                    setTimeout(function() {
                      $(".js-gallery-btn-next").removeClass('active');


                    }, 100);
                };
            if (e.keyCode == 37){
                    $( '.js-gallery-btn-prev' ).click();
                    $('.js-gallery-btn-prev').addClass('active');

                    // var curimage = $('.js-image-wrap.active');
                    setTimeout(function() {
                      $(".js-gallery-btn-prev").removeClass('active');
                    }, 100);
            };
        });


        //change image on click
        $('body').on('click', '.js-image-wrap', function(){
            $('.js-image-wrap').removeClass('active');
            $(this).addClass('active');
            image = $(this).find('.js-image').css('background-image');
            $('.js-main-image').css('background-image', image);

            /*var curItem = $('.js-image-wrap.active');
            var containerWidth = $('.container').width();
            var lengthNext = lenghtNextToActive(curItem, 1);
            var lengthPrev = lenghtNextToActive(curItem, 0);

            if (lengthNext > containerWidth) {

                thumbnailsGalleryClick();

            };*/

            //Stops moving left
            /*var containerWidth = $('.container').width();
            var imagesTotalWidth = 0;
            $.each($('.js-image-wrap'), function(){
                imagesTotalWidth = imagesTotalWidth + $('.js-image-wrap').outerWidth();
            });*/

            // console.log(imagesNextWidth);


           /* if (imagesTotalWidth > containerWidth) {
                console.log('less');
            }
            else    {
               console.log('moar');
            }*/

            imagecounter();
        });

        //resize whole gallery
        var container = $('.container').width();
        $('.js-gallery-container').css({'width' : container + "px"});
        $(window).resize(function(){
            var container = $('.container').width();
            $('.js-gallery-container').css({'width' : container + "px"});
        });

    //Take contact box
    $('body').on('click', '.js-make-contact-button', function(){
        $(this).closest('.js-contact-links').find('.js-make-contact').addClass('active');
        //$('body').css('overflow', 'hidden');
    }).on('click', '.js-close-make-contact', function(i9){
        $('.js-make-contact').removeClass('active');
        $('.js-form-submitted-message').removeClass('active');
        //$('body').css('overflow', 'visible');
    });

    $('html').click(function() {
        $('.js-make-contact.active').removeClass('active');
    });

    $('body').on('click', '.js-make-contact', function(event) {
        event.stopPropagation();
    });

    $('body').on('click', '.js-make-contact-button', function(event) {
        event.stopPropagation();
    });

    //services-content-hider
    $('body').on('click', '.js-btn-services-content-show', function(){
        $(this).addClass('hidden');
        $(this).closest('.services-content-hider').find('.js-btn-services-content-hide').removeClass('hidden');
        $(this).closest('.js-service-block').find('.js-services-user-added-content').addClass('open');
        $(this).closest('.js-service-block').find('.js-services-content-hider').addClass('open');
    });

    $('body').on('click', '.js-btn-services-content-hide', function(){
        $(this).addClass('hidden');
        $(this).closest('.services-content-hider').find('.js-btn-services-content-show').removeClass('hidden');
        $(this).closest('.js-service-block').find('.js-services-user-added-content').removeClass('open');
        $(this).closest('.js-service-block').find('.js-services-content-hider').removeClass('open');
    });


    // set cookie
    $('body').on('click', '.js-products-display-type-link', function(){
        $('.js-products-display-type-link, .js-products-display-type-content').removeClass('active');
        $(this).addClass('active');
        $('.js-products-display-type-content[identifier="' + $(this).attr('identifier') + '"]').addClass('active');
        setCookie('displayType', $(this).attr('identifier'));
    });


    if (getCookie('displayType')) {
        var displayType = getCookie('displayType');
        $('.js-products-display-type-link, .js-products-display-type-content').removeClass('active');
        $('.js-products-display-type-link[identifier="' + displayType + '"], .js-products-display-type-content[identifier="' + displayType + '"]').addClass('active');
    } else{
        $('.js-products-display-type-link[identifier="grid"], .js-products-display-type-content[identifier="grid"]').addClass('active');
    }

    // filter dropdown
    $('body').on('click', '.js-filter-dropdown-item', function(){
        var form = $(this).closest('form');
        var value = $(this).attr('identifier');
        form.find('[name="filters[term]"]').val(value);
        form.submit();
    });

    //mob-filter-dropdown
    $('body').on('click', '.js-mob-filter-btn', function(){
        $('.js-mob-filter-dropdown').toggleClass('active');
    });


    $.each($('.js-filter-mini-input'), function(){
        var filterGroupWidth = $('.filter-mini-group').outerWidth();
        var filterMiniLabelWidth = $('.filter-mini-group').find('.filter-mini-label').outerWidth();
        var filterMiniInputWidth = $('.filter-mini-group').find('.js-filter-mini-input').outerWidth();
        var newFilterMiniInputWidth = filterGroupWidth - filterMiniLabelWidth - 15;

        $(this).css({'width' : newFilterMiniInputWidth + "px"});
    });


        var filterGroupWidth = $('.filter-mini-group').outerWidth();
        var filterMiniLabelWidth = $('.filter-mini-group').find('.filter-mini-label').outerWidth();
        var jsfilterMiniInputWidth = filterGroupWidth - filterMiniLabelWidth - $('.js-filter-mini-btn').outerWidth() + 30;

        $('.js-filter-mini-lg-input').css({'width' : jsfilterMiniInputWidth + "px"});


    $(window).resize(function() {

        $.each($('.js-filter-mini-input'), function(){
            var filterGroupWidth = $('.filter-mini-group').outerWidth();
            var filterMiniLabelWidth = $('.filter-mini-group').find('.filter-mini-label').outerWidth();
            var filterMiniInputWidth = $('.filter-mini-group').find('.js-filter-mini-input').outerWidth();
            var newFilterMiniInputWidth = filterGroupWidth - filterMiniLabelWidth - 15;

            $(this).css({'width' : newFilterMiniInputWidth + "px"});
        });


            var filterGroupWidth = $('.filter-mini-group').outerWidth();
            var filterMiniLabelWidth = $('.filter-mini-group').find('.filter-mini-label').outerWidth();
            var jsfilterMiniInputWidth = filterGroupWidth - filterMiniLabelWidth - $('.js-filter-mini-btn').outerWidth() + 30;

            $('.js-filter-mini-lg-input').css({'width' : jsfilterMiniInputWidth + "px"});

    });

    // filter ajax search
        // brokers
        var request;
        $('body').on('keyup', '.js-search-broker', function(){
            var form = $(this).closest('form');
            var resultsContainer = $('.js-contacts-results');
            resultsContainer.empty().addClass('loading');
            if (request != null){
                request.abort();
                request = null;
            }

            request = $.ajax({
                url: nnaAjaxObject.ajaxUrl,
                type: 'post',
                dataType: 'json',
                data: {
                    action: 'filterBrokersByTitle',
                    formData: form.serialize(),
                },
                success: function(data) {
                    resultsContainer.removeClass('loading').append(ich.brokers({brokers: data}));
                    console.log(data);
                },
                error: function(){
                    resultsContainer.removeClass('loading');

                }
            });
        });
        // new devs
        $('body').on('keyup', '.js-search-new-devs', function(){
            var form = $(this).closest('form');
            var resultsContainer = $('.js-search-results');
            resultsContainer.empty().addClass('loading');
            if (request != null){
                request.abort();
                request = null;
            }

            request = $.ajax({
                url: nnaAjaxObject.ajaxUrl,
                type: 'post',
                dataType: 'json',
                data: {
                    action: 'filterNewDevsByTitle',
                    formData: form.serialize(),
                },
                success: function(data) {
                    resultsContainer.removeClass('loading').append(ich.newDevs({newDevs: data}));
                    console.log(data);
                },
                error: function(){
                    resultsContainer.removeClass('loading');

                }
            });
        });
    //

    $('.user-added-content table').addClass('table tablesaw tablesaw-stack');
    $('.user-added-content table').attr('data-tablesaw-mode','stack');

    $('body').on('click', '.js-back', function(){
        window.history.back();
        return false;
    });

    function imagecounter(){
        var amount = $('.js-image-wrap').length;
        var identifier = $('.js-image-wrap.active').find('.js-image').attr('image-identifier');
        var curAmount = parseInt(identifier) + 1;

        $('.js-total-amount').text(amount);
        $('.js-current-amount').text(curAmount);
    }

    // submit ajax submit
    $('body').on('submit', '.js-subscribe-newsletter', function(e){
        e.preventDefault();

        var form = $(this),
            language = form.find('.js-field-language').val(),
            email = form.find('.js-field-email').val(),
            isAgreedToPrivacyAgreement = form.find('.js-privacy-checkbox').is(':checked');

        form.removeClass('has-email-error has-privacy-error');

        if (isAgreedToPrivacyAgreement) {
            if (email.length > 0) {
                $.ajax({
                    method: 'post',
                    url: 'https://docs.google.com/forms/d/e/1FAIpQLSeuT_pdbpXAKee4nE1eQHo2PSOCEP70dGzwg_2fItoqjwZLpQ/formResponse',
                    data: {
                        'entry.943165678': email,
                        'entry.1945642353': language,
                    },
                    statusCode: {
                        0: function() {
                            subscribeSuccess(form);
                        },
                        200: function() {
                            subscribeSuccess(form);
                        }
                    }
                });
            } else {
                subscribeError(form, true);
            }
        } else {
            subscribeError(form, isAgreedToPrivacyAgreement);
        }
    });

    $(".js-fb-share").on("click",function(){
        var fbpopup = window.open("https://www.facebook.com/sharer/sharer.php?u=" + window.location.href, "pop", "width=600, height=400, scrollbars=no");
        return false;
    });

    function subscribeSuccess(form) {
        form.addClass('success');
        gaJoinNewsletter();
    }

    function subscribeError(form, privacy) {
        if (!privacy) {
            form.addClass('has-privacy-error');
        } else {
            form.addClass('has-email-error');
        }
    }

});

function cycleImages(){
    $('.js-slider-buttons-btn').addClass('disabled');
    var $active = $('.js-slider-single.active');
    var $next = ($active.next().length > 0) ? $active.next() : $('.js-slider .js-slider-single:first');

    var $activeBtn = $('.js-slider-buttons-btn.active');
    var $nextBtn = ($active.next().length > 0) ? $activeBtn.next() : $('.js-slider-buttons .js-slider-buttons-btn:first');

    $activeBtn.removeClass('active');
    $nextBtn.addClass('active');

    setTimeout(function() {
        $active.removeClass('active');//reset the z-index and unhide the image
        $next.addClass('active');//make the next image the top one
        $('.js-slider-buttons-btn').removeClass('disabled');
    }, 2000);
}


function setCookie(key, value) {
    var expires = new Date();
    expires.setTime(expires.getTime() + (1 * 24 * 60 * 60 * 1000));
    document.cookie = key + '=' + value + ';expires=' + expires.toUTCString();
}

function getCookie(key) {
    var keyValue = document.cookie.match('(^|;) ?' + key + '=([^;]*)(;|$)');
    return keyValue ? keyValue[2] : null;
}

/*function fixRowHeights(columns, rows) {
    var colOne = columns[0];
    var colTwo = columns[1];
    var colOneRows = $(colOne).find('.offer-details-block');
    var colTwoRows = $(colTwo).find('.offer-details-block');

    for (var i = colOneRows.length - 1; i >= 0; i--) {
        console.log("left" + $(colOneRows[i]).height());
    }

    for (var i = colTwoRows.length - 1; i >= 0; i--) {
        console.log("right" + $(colTwoRows[i]).height());
    }
}
*/

// Call polyfill to fit in images
/*
    objectFit.polyfill({
        selector: 'offer-thumbnail-img',
        fittype: 'cover',
    });
    objectFit.polyfill({
        selector: 'thumbnails-main-img',
        fittype: 'cover',
    });
    objectFit.polyfill({
        selector: 'project-thumbnail-img',
        fittype: 'cover',
    });
    objectFit.polyfill({
        selector: 'property-thumbnail-img',
        fittype: 'cover',
    });
    objectFit.polyfill({
        selector: 'estate-thumbnail-img',
        fittype: 'cover',
    });
    objectFit.polyfill({
        selector: 'mob-slider-img',
        fittype: 'cover',
    });
    objectFit.polyfill({
        selector: 'block-offer-thumbnail-img',
        fittype: 'cover',
    });*/

// Call slimscroll
   /* $('.js-images-list').slimscroll({
        size: '7px',
        alwaysVisible: true,
        distance: '5px',
        width: '100%',
        color: '#165528',
        opacity: 0.6,
    });*/


// console.log($('.mob-slider-item-wrap').length);
document.addEventListener('DOMContentLoaded', function () {

    if ( ! Modernizr.objectfit ) {
      $('.offer-thumbnail-img-wrap').each(function () {
        var $container = $(this),
            imgUrl = $container.find('img').prop('src');
        if (imgUrl) {
          $container
            .css('backgroundImage', 'url(' + imgUrl + ')')
            .addClass('object-fit-img-wrap');
        }
      });
    }


// Call slick
    if ($('.mob-slider-item-wrap').length > 1) {
        $('.js-mob-slider').slick({
          infinite: true,
          speed: 300,
          slidesToShow: 1,
          slidesToScroll: 1,
          centerMode: true,
          arrows: false,
          centerPadding: '20px'
        });
    }

    //Mobile counter
        var totAmount = $('.mob-slider-item-wrap').not('.slick-cloned').length;
        var mobSlide = $('.slick-active').attr('data-slick-index');
        var curAmount = parseInt(mobSlide) + 1;

        $('.js-mob-current-amount').text(curAmount);
        $('.js-mob-total-amount').text(totAmount);

    $('.slick-active').bind("DOMSubtreeModified",function(){
        var totAmount = $('.mob-slider-item-wrap').not('.slick-cloned').length;
        var mobSlide = $('.slick-active').attr('data-slick-index');
        var curAmount = parseInt(mobSlide) + 1;

        $('.js-mob-current-amount').text(curAmount);
        $('.js-mob-total-amount').text(totAmount);
    });

    /*$('.js-images-list').slick({
      infinite: false,
      speed: 300,
      slidesToShow: 6,
      slidesToScroll: 1,
      centerMode: false,
      arrows: true,
      centerPadding: '20px',
      touchMove:true,
    });*/
});




$(document).ready(function() {
  $('.price-row').click(function() {
    var $details = $(this).next('.price-details');
    if ($details.hasClass('collapsed')) {
      $('.price-details').removeClass('expanded').addClass('collapsed');
      $('.price-row').removeClass('active');
      $(this).addClass('active');
      $details.removeClass('collapsed').addClass('expanded');
    } else {
      $(this).removeClass('active');
      $details.removeClass('expanded').addClass('collapsed');
    }
  });

  $('.price-row').hover(
    function() {
      $(this).addClass('hover');
    },
    function() {
      $(this).removeClass('hover');
    }
  );

  $(document).click(function(event) {
    if (!$(event.target).closest('.price-table').length) {
      $('.price-details').removeClass('expanded').addClass('collapsed');
      $('.price-row').removeClass('active');
    }
  });
});

