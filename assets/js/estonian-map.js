$(function() {
    var ua = window.navigator.userAgent;
    var msie = ua.indexOf("MSIE ");

    var mapUrl = (msie > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./)) ? wpVariables.map_png : wpVariables.map_svg;

    $('#estonia-map').mapster({
        mapKey: 'name',
        configTimeout: 300000,
        singleSelect: true,
        isDeselectable: false,
        fadeDuration: 0,
        onClick: function(data) {
            let selCounty = data.e.currentTarget.attributes.getNamedItem('name').value;
            let offices = document.querySelectorAll('.em-tag[county-identifier=' + selCounty + ']');

            // If clicked county is active
            let activeOffice = document.querySelector('.em-tag.active')?.attributes.getNamedItem('county-identifier').value;
            if (activeOffice == selCounty) {
                return;
            }

            // Remove active office elements
            $('.em-tag.active').removeClass('active');
            $('.em-content.active').removeClass('active');

            offices.forEach((el) => {
                el.classList.add('active');
                document.querySelector('.em-content[identifier="' + el.attributes.getNamedItem('identifier').value + '"]')
                        ?.classList.add('active');
            });
        },
        areas: [
            {
                altImageOpacity: 1,
                fill: true,
                fillOpacity: 1,
                stroke: false,
                key: 'hiiumaa',
                altImage: wpVariables.templateDirectory + '/assets/img/' + mapUrl,
            },
            {
                altImageOpacity: 1,
                fill: true,
                fillOpacity: 1,
                stroke: false,
                key: 'viljandimaa',
                altImage: wpVariables.templateDirectory + '/assets/img/' + mapUrl,
            },
            {
                altImageOpacity: 1,
                fill: true,
                fillOpacity: 1,
                stroke: false,
                key: 'tartumaa',
                altImage: wpVariables.templateDirectory + '/assets/img/' + mapUrl,
            },
            {
                altImageOpacity: 1,
                fill: true,
                fillOpacity: 1,
                stroke: false,
                key: 'valgamaa',
                altImage: wpVariables.templateDirectory + '/assets/img/' + mapUrl,
            },
            {
                altImageOpacity: 1,
                fill: true,
                fillOpacity: 1,
                stroke: false,
                key: 'vorumaa',
                altImage: wpVariables.templateDirectory + '/assets/img/' + mapUrl,
            },
            {
                altImageOpacity: 1,
                fill: true,
                fillOpacity: 1,
                stroke: false,
                key: 'saaremaa',
                altImage: wpVariables.templateDirectory + '/assets/img/' + mapUrl,
            },
            {
                altImageOpacity: 1,
                fill: true,
                fillOpacity: 1,
                stroke: false,
                key: 'idaVirumaa',
                altImage: wpVariables.templateDirectory + '/assets/img/' + mapUrl,
            },
            {
                altImageOpacity: 1,
                fill: true,
                fillOpacity: 1,
                stroke: false,
                key: 'laanemaa',
                altImage: wpVariables.templateDirectory + '/assets/img/' + mapUrl,
            },
            {
                altImageOpacity: 1,
                fill: true,
                fillOpacity: 1,
                stroke: false,
                key: 'parnumaa',
                altImage: wpVariables.templateDirectory + '/assets/img/' + mapUrl,
            },
            {
                altImageOpacity: 1,
                fill: true,
                fillOpacity: 1,
                stroke: false,
                key: 'jogevamaa',
                altImage: wpVariables.templateDirectory + '/assets/img/' + mapUrl,
            },
            {
                altImageOpacity: 1,
                fill: true,
                fillOpacity: 1,
                stroke: false,
                key: 'harjumaa',
                altImage: wpVariables.templateDirectory + '/assets/img/' + mapUrl,
            },
            {
                altImageOpacity: 1,
                fill: true,
                fillOpacity: 1,
                stroke: false,
                key: 'raplamaa',
                altImage: wpVariables.templateDirectory + '/assets/img/' + mapUrl,
            },
            {
                altImageOpacity: 1,
                fill: true,
                fillOpacity: 1,
                stroke: false,
                key: 'laaneVirumaa',
                altImage: wpVariables.templateDirectory + '/assets/img/' + mapUrl,
            },
            {
                altImageOpacity: 1,
                fill: true,
                fillOpacity: 1,
                stroke: false,
                key: 'polvamaa',
                altImage: wpVariables.templateDirectory + '/assets/img/' + mapUrl,
            },
            {
                altImageOpacity: 1,
                fill: true,
                fillOpacity: 1,
                stroke: false,
                key: 'jarvamaa',
                altImage: wpVariables.templateDirectory + '/assets/img/' + mapUrl,
            },
        ],
    }).mapster('set', true, 'harjumaa');

    if (!$('.em-tags .em-tag').hasClass('active')) {
        $('.em-tags .em-tag').first().trigger('click').addClass('active');
        $('.em-tags-mob .em-tag-mob').first().addClass('active');
    }

    // map resize
    $('#estonia-map').mapster('resize', $('.map-wrapper').width(), 0, 0);
    $(window).resize(function (){
        $('#estonia-map').mapster('resize', $('.map-wrapper').width(), 0, 0);
    });

    // on tooltip hover
    $('body').on('mouseenter', '.em-tag', function(){
        var countyIdentifier = $(this).attr('county-identifier');
        $('.em-county[identifier="' + countyIdentifier + '"]').trigger('mouseenter');
    }).on('mouseleave', '.em-tag', function(){
        var countyIdentifier = $(this).attr('county-identifier');
        $('.em-county[identifier="' + countyIdentifier + '"]').trigger('mouseleave');
    });

    // Call slick
    $('.js-mob-map-slider').slick({
      infinite: true,
      speed: 300,
      slidesToShow: 1,
      slidesToScroll: 1,
      centerMode: true,
      arrows: true,
      centerPadding: '20px',
      prevArrow:'<button type="button" class="em-mob-map-btn em-mob-map-prev"></button>',
      nextArrow: '<button type="button" class="em-mob-map-btn em-mob-map-next"></button>'
    });


    $('.slick-active').bind("DOMSubtreeModified",function(){
        var identifier = $('.slick-active').attr('identifier');

        $('.em-tag[identifier="' + identifier + '"]').trigger('click');
    });


    // on tooltip clickd
    $('body').on('click', '.em-tag', function(e){
        var countyIdentifier = $(this).attr('county-identifier');
        document.querySelector('area[name="' + countyIdentifier + '"]')?.click()

    }).on('mouseenter', '.em-tag', function(){
        $(this).find('.em-tooltip').addClass('active');
    }).on('mouseleave', '.em-tag', function(){
        $(this).find('.em-tooltip').removeClass('active');
    });

        $('body').on('click', '.em-tag', function(){
            if($('.js-scrollable-heading').length && $( window ).width() >= 768 ) {
                $(document).scrollTop( $(".js-scrollable-heading").offset().top);  
            };
        });

    // mob next/prev
    /*$('body').on('click', '.em-prev, .em-next', function(){
        var direction = 0;
        if ($(this).hasClass('em-next')) {
            direction = 1;
        }
        slideCounty(direction);
    });

    $(".mob-map-controls-list").on("swiperight",function(){
        $('.em-prev').trigger('click');
    });
    $(".mob-map-controls-list").on("swipeleft",function(){
        $('.em-next').trigger('click');
    }); 

    function slideCounty(dir){
        if (!$('.em-tags-mob').hasClass('active')) {
            $('.em-tags-mob').addClass('active');
        } else{
            return;
        }
        var direction = dir;
        var active = $('.em-tag-mob.active');

        var prev = ($(active).prev().length ? $(active).prev() : $('.em-tags-mob .em-tag-mob').last());
        var next = ($(active).next().length ? $(active).next() : $('.em-tags-mob .em-tag-mob').first());
        prev.addClass('prev');
        next.addClass('next');
        if (direction) {
            active.addClass('to-right');
            next.addClass('to-active');
            $('.em-tag[identifier="' + next.attr('identifier') + '"]').trigger('click');
            setTimeout(function(){
                $('.em-tag-mob').removeClass('active to-right to-active next prev');
                next.addClass('active');
                $('.em-tags-mob').removeClass('active');
            }, 1000);
        } else{
            active.addClass('to-left');
            prev.addClass('to-active');
            $('.em-tag[identifier="' + prev.attr('identifier') + '"]').trigger('click');
            setTimeout(function(){
                $('.em-tag-mob').removeClass('active to-left to-active next prev');
                prev.addClass('active');
                $('.em-tags-mob').removeClass('active');
            }, 1000);
        }
    }*/
});
