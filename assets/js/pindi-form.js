$(function () {
	$('body').on('submit', '.js-wp-ajax-form', onWpAjaxFormSubmit);
	$('body').on('click', '.toggle-form', function(e){
		e.preventDefault();
		var target = $(this).data('target');
		$('.toggle-' + target).removeClass('hidden');
	});
});

function onWpAjaxFormSubmit(event) {
	event.preventDefault();

	var $that = $(this);
	$.each($that.find('.js-input'), function (index, element) {
		$(element).removeClass('has-error');

		if ($(element).hasClass('js-required') && !$(element).val()) {
			$(element).addClass('has-error');
		}
	});

	if ($that.find('.has-error').length) {
			return;
	}

	$.ajax({
			url: nnaAjaxObject.ajaxUrl,
			method: $that.attr('method'),
			data: $that.serialize(),
			success: onWpAjaxFormSuccess,
			error: onWpAjaxFormError,
	});
}

function onWpAjaxFormSuccess(response) {
	var $input = $('form input[name="action"][value="' + response.action + '"]');
	var $form = $input.closest('form');

	$form.find('.js-response').text('');
	if (response.status === 'success') {
		$form.html(response.message);
		return;
	}

	$form.find('.js-response').text(response.message);
}

function onWpAjaxFormError(error) {
	console.error(error);
}