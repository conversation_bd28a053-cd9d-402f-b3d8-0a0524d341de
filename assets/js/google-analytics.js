(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
})(window,document,'script','//www.google-analytics.com/analytics.js','ga');
/*
 * Add gtag for GA4
 */
//window.dataLayer = window.dataLayer || [];
//function gtag(){dataLayer.push(arguments);}
//gtag('js', new Date());
//gtag('config', 'G-4YLDWKDF6J');


ga('create', 'UA-73457234-1', 'auto'); // pindi
// ga('create', 'UA-73585733-1', 'auto'); // test.pindi
// ga('send', 'event', 'category', 'action', 'label');

ga('send', 'pageview');

$(function(){
	$('body').on('click', '.ga-banner', function(){
		let label = $(this).find('.ga-label').text();
		if ( ! label) label = 'Nimetu';
		gtag('event', 'klikk', {
		    'event_category' : 'banner',
		    'event_type' : 'klikk',
		    'event_label' : label,
		});
		ga('send', 'event', 'banner', 'klikk', label);

	}).on('click copy', 'a[href^="mailto:"]', function(event){
		let email = this.href.substr("mailto:".length);
		let type = (event.type == 'click') ? 'klikk' : 'kopeerimine';
		ga('send', 'event', 'meiliaadress', type, email);
		gtag('event', type, {
		    'event_category' : 'meiliaadress',
		    'event_type' : type,
		    'event_label' : email
		});

	}).on('click copy', 'a[href^="tel:"]', function(event){
		let nr = this.href.substr("tel:".length);
		let type = (event.type == 'click') ? 'klikk' : 'kopeerimine';
		ga('send', 'event', 'telnr', type, nr);
		gtag('event', type, {
		    'event_category' : 'telnr',
		    'event_type' : type,
		    'event_label' : nr
		});
	});

	document.querySelectorAll('.js-form-analytics')?.forEach((el) => {
		el.addEventListener('submit', (e) => {
			let form = e.currentTarget;
			if (form.dataset.gaCategory && form.dataset.gaIdentifier) {
				ga('send', 'event', form.dataset.gaCategory, 'kontakt', form.dataset.gaIdentifier);
				gtag('event', 'kontakt', {
				    'event_category' : form.dataset.gaCategory,
				    'event_type' : 'kontakt',
				    'event_label' : form.dataset.gaIdentifier
    				});
			}
		});
	});


});

    document.addEventListener( 'wpcf7submit', function( event ) {
//	console.log(event, gtag);
//        if ( '737069' == event.detail.contactFormId ) {
            // do something productive

        	const e_type = 'müügihinnangu päring';
	        ga('send', 'event', 'Form', 'Submit',  e_type + ' ' + event.detail.contactFormId);
	        gtag('event', 'Submit', {
	          'event_category' 	: 'Form',
	          'event_type' 		: 'Submit',
	          'event_label' 	: e_type + ' ' + event.detail.contactFormId,
	          'value' 		: event.detail.contactFormId
	        });
//        }
    }, false );

function gaJoinNewsletter() {
	ga('send', 'event', 'uudiskiri', 'liitumine', 'pindi uudiskiri');

    gtag('event', 'liitumine', {
      'event_category' : 'uudiskiri',
      'event_type' : 'liitumine',
      'event_label' :  'pindi uudiskiri'
    });
}

function gaTakeContact(item){
	var category, label;
	category = item.attr('ga-category');
	label = item.attr('ga-identifier');
	ga('send', 'event', category, 'kontakt', label);
    gtag('event', 'kontakt', {
      'event_category' : category,
      'event_type' : 'kontakt',
      'event_label' :  label
    });
}
