$(function(){
    toggleBanner();

    var link = document.querySelector(".js-modify-cookies");
    var additionalSettingArea = document.querySelector(".cookie-header-settings");
    var form = document.querySelector('form[name=cookie-bar-settings]');
    var popperInits = {};


    // Check if cookies have been agreed and that latest settings have been saved.
    let cookieSettings = getCookie("agreeCookies");
    try {
        cookieSettings = JSON.parse(cookieSettings);
        if (
            ! cookieSettings instanceof Object
            || cookieSettings.analytics === undefined
            || cookieSettings.preferences === undefined
            || cookieSettings.marketing === undefined
        ) {
            cookieSettings = false;
        }
    } catch (error) {
        cookieSettings = false;
    }

    if (cookieSettings) {
        updateCookieConsent(cookieSettings);
    } else {
        link.addEventListener("click", openAdditionalSettings);
        form.addEventListener('submit', saveSettings);
        showCookieBanner();
    }

    function saveSettings(e) {
        e.preventDefault();
        e.stopPropagation();

        let data = {
            'analytics': form.analytics.checked,
            'preferences': form.preferences.checked,
            'marketing': form.marketing.checked
        };
        setCookie("agreeCookies", JSON.stringify(data), {'max-age': 60*60*24*30*12});

        $('.js-cookie-header').addClass('cookie-header-hidden');
        updateCookieConsent(data);
    }

    function showCookieBanner() {
        const showEvents = ['mouseenter', 'focus'];
        const hideEvents = ['mouseleave', 'blur'];
        for(const optionDiv of document.querySelectorAll('.cookie-bar__check-group')) {
            let hoverToggleEl = optionDiv.querySelector('.tippy-toggle');
            popperInits[optionDiv.querySelector('.tippy-toggle').dataset.id] = Popper.createPopper(hoverToggleEl, optionDiv.querySelector('.cookiebar-tooltip'), {
                placement: 'top',
                modifiers: [{
                    name: 'offset',
                    options: {
                        offset: [0, 8],
                    },
                }],
            });

            for (const event of showEvents) {
                hoverToggleEl.addEventListener(event, e => displayHover(e));
            }
            for (const event of hideEvents) {
                hoverToggleEl.addEventListener(event, e => hideHover(e));
            }
        }
        $('.js-cookie-header').removeClass('cookie-header-hidden');
    }

    function updateCookieConsent(cookies) {
        if (
            cookies.analytics === false
            && cookies.preferences === false
            && cookies.marketing === false
        ) {
            return;
        }

        let consent = {};
        if (typeof gtag === "function") {
            if (cookies.analytics) consent.analytics_storage = 'granted';
            if (cookies.marketing) consent.ad_user_data = 'granted';
            if (cookies.preferences) consent.ad_storage = 'granted';
            gtag('consent', 'update', consent);
        }
    }

    function openAdditionalSettings(e) {
        e.preventDefault();

        document.querySelector(".cookie-header-buttons").classList.add("hide");
        additionalSettingArea.classList.add("show");
    }

    // returns the cookie with the given name,
    // or undefined if not found
    function getCookie(name) {
      let matches = document.cookie.match(new RegExp(
        "(?:^|; )" + name.replace(/([\.$?*|{}\(\)\[\]\\\/\+^])/g, '\\$1') + "=([^;]*)"
      ));
      return matches ? decodeURIComponent(matches[1]) : undefined;
    }

    function setCookie(name, value, attributes = {}) {
      attributes = {
        path: '/',
        SameSite: 'Lax',
        // add other defaults here if necessary
        ...attributes
      };

      if (attributes.expires instanceof Date) {
        attributes.expires = attributes.expires.toUTCString();
      }

      let updatedCookie = encodeURIComponent(name) + "=" + encodeURIComponent(value);

      for (let attributeKey in attributes) {
        updatedCookie += "; " + attributeKey;
        let attributeValue = attributes[attributeKey];
        if (attributeValue !== true) {
          updatedCookie += "=" + attributeValue;
        }
      }

      document.cookie = updatedCookie;
    }

    function displayHover(e) {
        let popperID = e.currentTarget.dataset.id;
        e.currentTarget.nextElementSibling.setAttribute('data-show', '');

        // We need to tell Popper to update the tooltip position
        // after we show the tooltip, otherwise it will be incorrect
        popperInits[popperID].update();
    }

    function hideHover(e) {
        e.currentTarget.nextElementSibling.removeAttribute('data-show');
    }

    $('body').on('click', '.js-ad-banner-close', function() {
        $(this).closest('.js-ad-banner').removeClass('ad-banner--visible');
    });

    function toggleBanner() {
        setTimeout(function() {
            $('.js-ad-banner').addClass('ad-banner--visible');
        }, 10 * 1000);
    }
});

