

/* Invisible outside of print
 * -------------------------------------------------------------------------- */

	.logo.printable-logo,
	.printable-thumbnails {
		display: none;
	}

@media print {

	@page {
	    size: auto;
	    margin: 5mm;
	}


	* {
		-webkit-print-color-adjust: exact!important;
	}
	/* Overwrite bootstrap to fit print(massive code!)
	 * -------------------------------------------------------------------------- */
		@media print {
		  .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12 {
			float: left;
		  }
		  .col-sm-12 {
			width: 100%;
		  }
		  .col-sm-11 {
			width: 91.66666667%;
		  }
		  .col-sm-10 {
			width: 83.33333333%;
		  }
		  .col-sm-9 {
			width: 75%;
		  }
		  .col-sm-8 {
			width: 66.66666667%;
		  }
		  .col-sm-7 {
			width: 58.33333333%;
		  }
		  .col-sm-6 {
			width: 50%;
		  }
		  .col-sm-5 {
			width: 41.66666667%;
		  }
		  .col-sm-4 {
			width: 33.33333333%;
		  }
		  .col-sm-3 {
			width: 25%;
		  }
		  .col-sm-2 {
			width: 16.66666667%;
		  }
		  .col-sm-1 {
			width: 8.33333333%;
		  }
		  .col-sm-pull-12 {
			right: 100%;
		  }
		  .col-sm-pull-11 {
			right: 91.66666667%;
		  }
		  .col-sm-pull-10 {
			right: 83.33333333%;
		  }
		  .col-sm-pull-9 {
			right: 75%;
		  }
		  .col-sm-pull-8 {
			right: 66.66666667%;
		  }
		  .col-sm-pull-7 {
			right: 58.33333333%;
		  }
		  .col-sm-pull-6 {
			right: 50%;
		  }
		  .col-sm-pull-5 {
			right: 41.66666667%;
		  }
		  .col-sm-pull-4 {
			right: 33.33333333%;
		  }
		  .col-sm-pull-3 {
			right: 25%;
		  }
		  .col-sm-pull-2 {
			right: 16.66666667%;
		  }
		  .col-sm-pull-1 {
			right: 8.33333333%;
		  }
		  .col-sm-pull-0 {
			right: auto;
		  }
		  .col-sm-push-12 {
			left: 100%;
		  }
		  .col-sm-push-11 {
			left: 91.66666667%;
		  }
		  .col-sm-push-10 {
			left: 83.33333333%;
		  }
		  .col-sm-push-9 {
			left: 75%;
		  }
		  .col-sm-push-8 {
			left: 66.66666667%;
		  }
		  .col-sm-push-7 {
			left: 58.33333333%;
		  }
		  .col-sm-push-6 {
			left: 50%;
		  }
		  .col-sm-push-5 {
			left: 41.66666667%;
		  }
		  .col-sm-push-4 {
			left: 33.33333333%;
		  }
		  .col-sm-push-3 {
			left: 25%;
		  }
		  .col-sm-push-2 {
			left: 16.66666667%;
		  }
		  .col-sm-push-1 {
			left: 8.33333333%;
		  }
		  .col-sm-push-0 {
			left: auto;
		  }
		  .col-sm-offset-12 {
			margin-left: 100%;
		  }
		  .col-sm-offset-11 {
			margin-left: 91.66666667%;
		  }
		  .col-sm-offset-10 {
			margin-left: 83.33333333%;
		  }
		  .col-sm-offset-9 {
			margin-left: 75%;
		  }
		  .col-sm-offset-8 {
			margin-left: 66.66666667%;
		  }
		  .col-sm-offset-7 {
			margin-left: 58.33333333%;
		  }
		  .col-sm-offset-6 {
			margin-left: 50%;
		  }
		  .col-sm-offset-5 {
			margin-left: 41.66666667%;
		  }
		  .col-sm-offset-4 {
			margin-left: 33.33333333%;
		  }
		  .col-sm-offset-3 {
			margin-left: 25%;
		  }
		  .col-sm-offset-2 {
			margin-left: 16.66666667%;
		  }
		  .col-sm-offset-1 {
			margin-left: 8.33333333%;
		  }
		  .col-sm-offset-0 {
			margin-left: 0%;
		  }
		  .visible-xs {
			display: none !important;
		  }
		  .hidden-xs {
			display: block !important;
		  }
		  table.hidden-xs {
			display: table;
		  }
		  tr.hidden-xs {
			display: table-row !important;
		  }
		  th.hidden-xs,
		  td.hidden-xs {
			display: table-cell !important;
		  }
		  .hidden-xs.hidden-print {
			display: none !important;
		  }
		  .hidden-sm {
			display: none !important;
		  }
		  .visible-sm {
			display: block !important;
		  }
		  table.visible-sm {
			display: table;
		  }
		  tr.visible-sm {
			display: table-row !important;
		  }
		  th.visible-sm,
		  td.visible-sm {
			display: table-cell !important;
		  }
		}

		.hidden{display:none;visibility:hidden}
		.visible-phone{display:none!important}
		.visible-tablet{display:none!important}
		.hidden-desktop{display:none!important}
		.visible-desktop{display:inherit!important}
	/* print-col
	 * -------------------------------------------------------------------------- */
		.print-col-5 {width:41.666%; float:left;}
		.print-col-6 {width:50%; float:left;}
		.print-col-7 {width:58.333%; float:left;}
		.print-col-12 {width:100%; right: 0; left: 0;}

		@media(max-width: 800px) {
			.print-col-sm-12 {width:100%; right: 0; left: 0;}
		}
	/* print-txt
	 * -------------------------------------------------------------------------- */
		.print-txt-center {
			text-align: center!important;
		}
	/* Removed items
	 * -------------------------------------------------------------------------- */

		.footer,
		.offer-more,
		.thumbnails-block-item,
		.lang-link,
		.nav,
		.offer-header,
		.logo-img:after,
		.contact-info-link:after,
		.contact-details-item-link:after,
		.offer-links,
		.services-links,
		.main-filter,
		.contact-links,
		.offer-price-amount-button-wrap,
		.contact-heading,
		.offer-content,
		.gallery,
		.gallery-title,
		.page-offer-single .header,
		.content-divider:before, .content-divider:after,
		a[href]:after {
			display: none!important;
		}
	/* Visible items
	 * -------------------------------------------------------------------------- */
		.thumbnails-main,
		.thumbnails-main-abs,
		.thumbnails-main-img,
		.offer-thumbnails,
		.col-sm-6,
		.logo.printable-logo .logo-img,
		.printable-thumbnails,
		.printable-thumbnails-block-item,
		.printable-offer-content {
			display: block!important;
		}
	/* Printable-header
	 * -------------------------------------------------------------------------- */

		.printable-header {
			margin-bottom: 15px;
			padding-bottom: 5px;
			border-bottom: 1px solid #d9d9d9;
		}
	/* Logo
	 * -------------------------------------------------------------------------- */
		.logo.printable-logo {
			position: relative;
			top: 0;
			margin-top: 20px;
			display: inline-block!important;
			width: 40%;
			text-align: right;
			vertical-align: middle;
			max-width: none;
		}

		.printable-logo-link {
			line-height: 60px;
			display: block;
		}

		.printable-logo-link:after {
			content: none!important;
		}
	/* Offer-name
	 * -------------------------------------------------------------------------- */
		.lg-offer-name {
			font-size: 17px;
			margin-bottom:25px;
		}

		.lg-offer-name .offer-name-extra {
			font-size: 15px;
		}
	/* Offer-thumbnail
	 * -------------------------------------------------------------------------- */

		.offer-thumbnail-img-wrap {
			page-break-inside: avoid;
		}
	/* featured-products
	 * -------------------------------------------------------------------------- */
		.featured-products {
			page-break-inside: avoid;
		}
	/* Offer-details/Offer-price
	 * -------------------------------------------------------------------------- */
		.offer-details-label, .offer-details-content {vertical-align:top;font-size: 15px;}
		.offer-price-amount {font-size: 30px;}
		.offer-price-per-area {display: block;}
		.offer-content-group {
			page-break-inside: avoid;
		}
	/* printable-object-type
	 * -------------------------------------------------------------------------- */
		.printable-object-type {
			display: inline-block;
			width: 60%;
			margin-right: -5px;
			vertical-align: middle;
			font-family: "source_sans_prosemibold", "Helvetica Neue", Helvetica, arial, sans-serif;
			font-size: 15px;
			color: #165528!important;
		}
	/* Flags
	 * -------------------------------------------------------------------------- */

		.flag-item {-webkit-print-color-adjust: exact;}
		.flag-et {background-image: url("../img/et.png")!important;}
		.flag-en {background-image: url("../img/en_GB.png")!important;}
		.flag-ru{background-image: url("../img/ru_RU.png")!important;}
		.flag-fi{background-image: url("../img/fi.png")!important;}
		.flag-it{background-image: url('../img/it.png')!important;}
	/* Contact
	 * -------------------------------------------------------------------------- */
		.contact-thumbnail {
			width: 30%;
		}
		.contact-info {
			width: 68%;
		}
	/* Printable thumbnails
	 * -------------------------------------------------------------------------- */

		.thumbnails-row {
			margin-bottom: 10px;
			page-break-inside: avoid;
		}

		.thumbnails-row:after {
			content: "";
			display: table;
			clear: both;
		}

		.printable-thumbnails {
			padding-top: 20px;
			bottom-top: 20px;
			margin-top: 30px;
			border-top: 1px solid #e7e7e7;
			border-bottom: 1px solid #e7e7e7;
		}

		.printable-thumbnails-block-item {
			width: 32.25%;
			padding-bottom: 32%;
			page-break-inside: avoid;
		}
	/* Font colors
	 * -------------------------------------------------------------------------- */

		.offer-name,
		.offer-price-amount,
		.offer-content-heading,
		.contact-info-link,
		.contact-details-item,
		 .contact-details-item-link {
			color: #165528!important;
		}

		.offer-name-extra,
		.contact-info-title {
			color: #8ba693!important;
		}
	/* Overall font related
	 * -------------------------------------------------------------------------- */

		.offer-content-text p {
			page-break-inside: avoid;
		}
	/* Table-contacts
	 * -------------------------------------------------------------------------- */
	 	.table-contacts tr,
	 	.table-contacts td {
	 		display:table-row!important;
	 	}

	 	.table-contacts tbody tr td .contact-thumbnail,
	 	.table-contacts tbody tr td .contact-info {
	 		text-align: right;
	 	}

	 	.table-contacts tbody tr td:last-child .contact-thumbnail,
	 	.table-contacts tbody tr td:last-child .contact-info {
	 		text-align: left;
	 	}

	 	.table-contacts tbody tr td {display:table-cell!important;width: 50%;}
	/* Other
	 * -------------------------------------------------------------------------- */

		.offer-price {
			border-bottom: 1px solid #d9d9d9!important;
		}

		.offer-contact {
			border: 0;
		}

		.services-user-added-content {
			height: auto;
		}
	/* Contact
	 * -------------------------------------------------------------------------- */

		.offer-contact {
			float: right;
			width: 58.333%;
			padding:0 15px;
			margin: 0;
			page-break-inside: avoid;
		}


		.contact-thumbnail-img {
			width: 100px;
			height: 100px;
		}


		.contact-details {
			margin-bottom: 0;
		}

		.contact-thumbnail {
		    width: 25%;
		    min-width: 100px;
		}

		.contact-info {
		    width: 72%;
		    max-width: 230px;
		}

		@media(max-width: 767px) {
			.contact-thumbnail {
				width: 100%;
				min-width: none;
				margin-bottom: 10px;
				text-align: left;
			}

			.contact-info {
				width: 100%;
				max-width: none;
			}
		}

}
