<div class="sidebar-rates">
    <?php
        global $infoPageId;
        $facebook = get_field('facebook_link', pll_get_post($infoPageId));
        $linkedin = get_field('linkedin_link', pll_get_post($infoPageId));
        $instagram = get_field('instagram_link', pll_get_post($infoPageId));
        $rss = get_field('rss_link', pll_get_post($infoPageId));

        $index = get_field('index_percentage', pll_get_post($infoPageId));
        $indexVal = get_field('index_value', pll_get_post($infoPageId));
        $indexIsRaising = get_field('index_is_raising', pll_get_post($infoPageId));
        $indexLastUpdated = get_field('index_last_updated', pll_get_post($infoPageId));
        $offersCount = NnaApi::getInstance()->getObjectsCount();
        $args = array(
            'posts_per_page'   => -1,
            'post_type'        => 'new-developments',
            'post_status'      => 'publish',
        );
        $newDevsCount = count(get_posts($args));

        //Get id for market survey page
        $marketSurveyArgs = [
            'post_type' => 'page',
            'fields' => 'ids',
            'nopaging' => true,
            'meta_key' => '_wp_page_template',
            'meta_value' => 'page-templates/market_surveys.php'
        ];
        $marketSurveyPageId = array_values(get_posts($marketSurveyArgs))[0];
    ?>

    <a href="<?php echo get_the_permalink($marketSurveyPageId); ?>" class="link-button link-button-secondary"><?php pll_e('Market analysis'); ?></a>
    <div class="stats">
        <?php if ($index || $indexVal) { ?>
            <div class="stats-index">
                <div class="factor">
                    <div class="factor-label"><?php pll_e('Pindi index'); ?>:</div>
                    <?php if ($index) { ?>
                        <a href="<?php echo get_the_permalink($marketSurveyPageId); ?>" class="factor-data factor-data-lg factor-data-link <?php echo ($indexIsRaising == 'asc' ? 'ascending' : '') . ($indexIsRaising == 'desc' ? 'descending' : ''); ?>"><?php echo $index; ?></a>
                    <?php } ?>

                    <?php if ($indexVal) { ?>
                    <div class="factor-details">
                        <?php echo $indexVal; ?>
                    </div>
                    <?php } ?>
                </div>
            </div>
        <?php } ?>

        <div class="stats-offers">
            <div class="factor">
                <div class="factor-label"><?php pll_e('Total offers'); ?>:</div>
                <div class="factor-data"><?php echo $offersCount; ?></div>
            </div>
        </div>
        <div class="stats-projects">
            <div class="factor">
                <div class="factor-label"><?php pll_e('New development projects'); ?>:</div>
                <div class="factor-data"><?php echo $newDevsCount; ?></div>
            </div>
        </div>
        <span class="stats-date">
            <?php echo $indexLastUpdated; ?>
        </span>

    </div>
</div>
<div class="sidebar-social-icons">
    <ul class="social-icons">
        <li class="social-icons-item">
            <a class="social-icons-link" href="<?php echo $facebook ;?>" target="_BLANK">
              <i class="fa fa-facebook social-icons-link-icon social-icons-link-fb"></i>
            </a>
        </li>
        <li class="social-icons-item">
            <a class="social-icons-link" href="<?php echo $linkedin ;?>" target="_BLANK">
              <i class="fa fa-linkedin social-icons-link-icon"></i>
            </a>
        </li>
        <li class="social-icons-item">
            <a class="social-icons-link" href="<?php echo $rss; ?>" target="_BLANK">
              <i class="fa fa-rss social-icons-link-icon"></i>
            </a>
        </li>
        <li class="social-icons-item">
            <a class="social-icons-link" href="<?php echo $instagram; ?>" target="_BLANK">
              <i class="fa fa-instagram social-icons-link-icon"></i>
            </a>
        </li>
    </ul>
</div>
