<?php
	if ($contact) { ?>

		<div class="em-content contact broker-contact-card mb-30">
			<?php if ($broker['title']): ?>
				<div class="contact-heading mb-20"><h2><?php echo $broker['title']; ?></h2></div>
			<?php endif; ?>
			<div class="contact-thumbnail">
				<?php if ($contact['thumbnail']): ?>
					<img src="<?php echo $contact['thumbnail']['sizes']['medium']; ?>" alt="<?php echo $contact['thumbnail']['alt']; ?>" class="contact-thumbnail-img">
				<?php else: ?>
					<img src="<?php echo get_template_directory_uri(); ?>/assets/img/no_pic_placeholder.png" alt="No image available" class="contact-thumbnail-img">
				<?php endif; ?>
			</div>
			<div class="contact-info">
				<?php if ($contact['name']): ?>
					<div class="contact-info-name"><a class="contact-info-link"><?php echo $contact['name']; ?></a></div>
				<?php endif ?>
				<?php if ($contact['occupation']): ?>
					<div class="contact-info-title"><?php echo $contact['occupation']; ?></div>
				<?php endif ?>

				<?php if ($contact['languages']): ?>
					<div class="contact-languages">
						<div class="flags">
							<ul class="flags-list">
								<?php
									foreach ($contact['languages'] as $lang) {
										echo '<li class="flag-item flag-' . $lang . '"></li>';
									}
								?>
							</ul>
						</div>
					</div>
				<?php endif ?>
				<ul class="contact-details">
					<?php if ($contact['phone']): ?>
						<li class="contact-details-item">
							<!-- <?php pll_e('Phone'); ?>: -->
							<a class="contact-details-item-link" href="tel:<?php echo $contact['phone']; ?>">
								<?php echo $contact['phone'] ?>
							</a>
						</li>
					<?php endif; ?>
					<?php if ($contact['mobile']): ?>
						<li class="contact-details-item">
							<!-- <?php pll_e('Mobile'); ?>: -->
							<a class="contact-details-item-link" href="tel:<?php echo $contact['mobile']; ?>">
								<?php echo $contact['mobile'] ?>
							</a>
						</li>
					<?php endif; ?>
					<?php if ($contact['email']): ?>
						<li class="contact-details-item">
							<!-- <?php pll_e('Email'); ?>: -->
							<a class="contact-details-item-link" href="mailto:<?php echo $contact['email']; ?>"><?php echo $contact['email'] ?></a>
						</li>
					<?php endif; ?>
				</ul>
				<div class="js-contact-links contact-links">
					<div class="make-contact js-make-contact">
						<div class="make-contact-lightbox js-close-make-contact">
						</div>
						<div class="make-contact-box mailto">
							<div class="mailto-heading">
								<div class="gallery-close-button mailto-close-button js-close-make-contact"></div>
								<h4 class="mailto-heading-header"><?php pll_e('Contact us') ?></h4>
							</div>
							<div class="mailto-content clearfix">
								<div class="mailto-content-contact">
									<div class="contact">
										<div class="contact-thumbnail contact-thumbnail-block">
											<?php if ($contact['thumbnail']): ?>
												<img src="<?php echo $contact['thumbnail']['sizes']['medium']; ?>" alt="<?php echo $contact['thumbnail']['alt']; ?>" class="contact-thumbnail-img">
											<?php else: ?>
												<img src="<?php echo get_template_directory_uri(); ?>/assets/img/no_pic_placeholder.png" alt="No image available" class="contact-thumbnail-img">
											<?php endif; ?>
										</div>
										<div class="contact-info contact-info-block">
											<?php
												if ($contact['name']) {
													echo '<div class="contact-info-name">' . $contact['name'] . '</div>';
												}
												if ($contact['occupation']){
													echo '<div class="contact-info-title">' . $contact['occupation'] . '</div>';
												}

												if ($contact['languages']){
													echo '<div class="contact-languages">';
													echo '<div class="flags">';
													echo '<ul class="flags-list">';
													foreach ($contact['languages'] as $lang) {
														echo '<li class="flag-item flag-' . $lang . '"></li>';
													}
													echo '</ul>';
													echo '</div>';
													echo '</div>';
												}
											?>
											<ul class="contact-details">
												<?php if ($contact['phone']): ?>
													<li class="contact-details-item">
														<!-- <?php pll_e('Phone'); ?>: -->
														<a class="contact-details-item-link" href="tel:<?php echo $contact['phone']; ?>">
															<?php echo $contact['phone'] ?>
														</a>
													</li>
												<?php endif; ?>
												<?php if ($contact['mobile']): ?>
													<li class="contact-details-item">
														<!-- <?php pll_e('Mobile'); ?>: -->
														<a class="contact-details-item-link" href="tel:<?php echo $contact['mobile']; ?>">
															<?php echo $contact['mobile'] ?>
														</a>
													</li>
												<?php endif; ?>
												<?php if ($contact['email']): ?>
													<li class="contact-details-item">
														<!-- <?php pll_e('Email'); ?>: -->
														<a class="contact-details-item-link" href="mailto:<?php echo $contact['email']; ?>">
															<?php echo $contact['email'] ?>
														</a>
													</li>
												<?php endif; ?>
											</ul>
										</div>
									</div>
								</div>

								<form class="ga-contact-form js-contact-person-form mailto-content-form clearfix" identifier-action="sendMailTakeContact" ga-identifier="<?php echo get_the_permalink(); ?>" ga-category="Esileht">
									<div class="js-form-submitted-message mailto-form-after-submit-message">
									</div>
									<div class="mailto-form-group js-field">
										<input name="mailForm[id]" type="text" value="<?php echo $contact['contactID']; ?>" hidden>
										<label class="mailto-label">
											<?php pll_e('Name'); ?>
										</label>
										<input name="mailForm[name]" type="text" class="js-input js-required mailto-form-input">
										<span class='js-error'></span>
									</div>
									<div class="mailto-form-group mailto-form-group-last js-field">
										<label class="mailto-label">
											<?php pll_e('Email'); ?>
										</label>
										<input name="mailForm[email]" type="text" class="js-input js-required js-email mailto-form-input">
										<span class='js-error'></span>
									</div>
									<div class="clearfix"></div>

									<div class="mailto-form-group-full js-field">
										<label class="mailto-label">
											<?php pll_e('Message content'); ?>
										</label>
										<textarea name="mailForm[message]" class="js-input js-required mailto-form-textarea"></textarea>
										<span class='js-error'></span>
									</div>
									<button class="js-contact-person-form-submit btn main-button" type="button"><?php pll_e('Send message'); ?></button>
								</form>
							</div>
						</div>
					</div>

					<button class="btn main-button contact-links-button js-make-contact-button">
						<?php pll_e('Take contact') ?>
					</button>

				</div>
			</div>
		</div>
<?php
}
